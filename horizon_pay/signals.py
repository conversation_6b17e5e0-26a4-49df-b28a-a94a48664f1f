from django.conf import settings
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver

from horizon_pay.helpers.helper_function import register_terminal_cards_app
from kyc_app.models import BVNDetail, DocumentFaceMatchKYC2Detail, GuarantorDetail, KYCTable
from horizon_pay.models import HorizonPayTable, StartCashOutTran, TransactionOwners, TerminalSerialTable, TerminalRetrieval
from horizon_pay.tasks import task_to_save_other_haptics_data
import requests


@receiver(post_save, sender=HorizonPayTable)
def save_other_haptics_data(sender, instance: HorizonPayTable, created, **kwargs):

    if instance.response_code and instance.amount and instance.terminal_id:
        pass
    else:
        task_to_save_other_haptics_data.delay(instance.id)


@receiver(post_save, sender=StartCashOutTran)
def dispatch_trx_status_to_owner(sender, instance: StartCashOutTran, created, **kwargs):

    if not created:
        if instance.trx_owner and not instance.trx_owner_alerted and instance.trans_complete:

            trx_owner = TransactionOwners.objects.filter(owner_code=instance.trx_owner)
            if trx_owner.exists():

                url = trx_owner.last().postback_url

                payload = dict(
                    rrn=instance.rrn,
                    amount=instance.amount_started,
                    status=instance.trans_complete,
                    transaction_status=instance.transaction_status
                )

                # if instance.trx_owner == "PAYBOX":
                #     payload = dict(
                #         rrn=instance.rrn,
                #         amount=instance.amount_resolved,
                #         status=instance.trans_complete,
                #         transaction_status=instance.transaction_status
                #     )
                # else:
                #     payload = dict(
                #         rrn=instance.rrn,
                #         amount=instance.amount_started,
                #         status=instance.trans_complete,
                #         transaction_status=instance.transaction_status
                #     )

                instance.trx_owner_alerted = True
                instance.save()

                response = requests.post(url=url, json=payload)

                instance.trx_owner_response_payload = response.text
                instance.save()

        else:
            pass
            # task_to_save_other_haptics_data.delay(instance.id)


@receiver(post_save, sender=TerminalSerialTable)
def update_terminal_record_on_pos_backend(sender, instance: TerminalSerialTable, created, **kwargs):
    payload = [
        {
            "deviceSerialNumber": instance.terminal_serial,
            "nibssTerminalId": instance.terminal_id,
            "iswTerminalId": instance.isw_terminal_id,
            "arcaPayTerminalId": instance.arca_terminal_id
        }
    ]
    register_terminal_cards_app(data=payload)

    return True


@receiver(pre_save, sender=TerminalSerialTable)
def update_terminal_user(sender, instance, **kwargs):
    if instance.pk:
        old_instance = sender.objects.get(id=instance.pk)
        if old_instance.user != instance.user:
            # A terminal re-assignment is happening!!! Check terminal retrieval exists
            TerminalRetrieval.objects.filter(terminal=instance).update(reassigned=True, reassigned_to=instance.user, status="reassigned")

    return True



