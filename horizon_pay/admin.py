import json
from django.contrib import admin
from django.contrib.admin.views.main import ChangeList
from django.contrib.auth.models import Group
from django.db.models import Q

from import_export import resources
from import_export.admin import ImportExportMixin, ImportExportModelAdmin
from horizon_pay.models import *


class TransactionOwnersResource(resources.ModelResource):

    class Meta:
        model = TransactionOwners


class HorizonPayTableResource(resources.ModelResource):

    class Meta:
        model = HorizonPayTable


class CardTransactionResource(resources.ModelResource):

    class Meta:
        model = CardTransaction


class CardSignatureVerificationResource(resources.ModelResource):

    class Meta:
        model = CardSignatureVerification


class CardTransaactionRawPayloadResource(resources.ModelResource):

    class Meta:
        model = CardTransaactionRawPayload


class CardIssuerTableResource(resources.ModelResource):
    class Meta:
        model = CardIssuerTable


class TerminalSerialTableResource(resources.ModelResource):

    all_rows = []

    class Meta:
        model = TerminalSerialTable

    def get_existing_row_from_database(self, id, term: str):
        try:
            inst = TerminalSerialTable.objects.get(id=int(id))
            if term == "ISW":
                return inst.isw_terminal_id
            elif term == "ARCA":
                return inst.arca_terminal_id

        except:
            return None

    def before_import_row(self, row, **kwargs):
        # Generate the unique value for 'field1' using your custom function

        if len(row['isw_terminal_id']) == 0:
            existing_row = self.get_existing_row_from_database(id=row["id"], term="ISW")

            if existing_row is None:
                unique_value = self.generate_isw_unique_value()
                row['isw_terminal_id'] = unique_value
                try:
                    ISWTerminalTable.objects.filter(isw_terminal_id=unique_value).update(
                        assigned = True
                    )
                except:
                    pass

            else:
                row['isw_terminal_id'] = existing_row

        if len(row['arca_terminal_id']) == 0:
            existing_row = self.get_existing_row_from_database(id=row["id"], term="ARCA")

            if existing_row is None:
                unique_value = self.generate_arca_unique_value()
                row['arca_terminal_id'] = unique_value
                try:
                    ArcaPayTerminalTable.objects.filter(arca_terminal_id=unique_value).update(
                        assigned = True
                    )
                except:
                    pass

            else:
                row['arca_terminal_id'] = existing_row

    def after_import(self, dataset, result, using_transactions, dry_run, **kwargs):
        if dry_run == False:
            data_list = []
            for row in dataset.dict:
                try:
                    get_data = TerminalSerialTable.objects.get(id=row["id"])

                    print(get_data)
                    print(get_data.isw_terminal_id)

                    if not get_data.is_registered or get_data.isw_terminal_id != row["isw_terminal_id"] or get_data.arca_terminal_id != row["arca_terminal_id"]:
                        data_list.append(
                            {
                                "deviceSerialNumber": get_data.terminal_serial,
                                "nibssTerminalId": get_data.terminal_id,
                                "iswTerminalId": get_data.isw_terminal_id,
                                "arcaPayTerminalId": get_data.arca_terminal_id
                            }
                        )

                except:
                    pass

            print(data_list)

            from .tasks import batch_insert_terminal_ids_task

            batch_insert_terminal_ids_task.apply_async(
                queue="horizon_queue",
                kwargs={
                    "data_list": data_list
                }
            )

            # insert_to_cards_app = register_terminal_cards_app(data=data_list)
            # print(insert_to_cards_app)

        return

    def generate_isw_unique_value(self):
        return ISWTerminalTable.generate_isw_tids()

    def generate_arca_unique_value(self):
        return ArcaPayTerminalTable.generate_arca_tids()


class StartCashOutTranResource(resources.ModelResource):
    class Meta:
        model = StartCashOutTran


class RecordTerminalSerialResource(resources.ModelResource):
    class Meta:
        model = RecordTerminalSerial


class ManualRefundResource(resources.ModelResource):
    class Meta:
        model = ManualRefund


class ISWTerminalTableResource(resources.ModelResource):
    class Meta:
        model = ISWTerminalTable


class NewCardSaveDataTableResource(resources.ModelResource):
    class Meta:
        model = NewCardSaveDataTable


class DebugTerminalResource(resources.ModelResource):
    class Meta:
        model = DebugTerminal


class ArcaPayTerminalTableResource(resources.ModelResource):
    class Meta:
        model = ArcaPayTerminalTable

#######################################################################
# ADMINS


class HorizonPayTableResourceAdmin(ImportExportModelAdmin):

    def re_verify_transactions(self, request, queryset):
        if queryset.filter(is_resolved=True).exists():
            self.message_user(request, "Error. There is a verified transaction in selected items")
        else:
            for query in queryset:
                from horizon_pay.tasks import accept_cashout_resolve_task

                rrn = query.rrn
                terminal_id = query.terminal_id
                amount = query.amount
                response_json = json.loads(query.payload)
                force_resolve = query.force_resolve

                accept_cashout_resolve_task.apply_async(
                    queue="resolvecashout",
                    kwargs={
                        "rrn": rrn,
                        "terminal_id": terminal_id,
                        "amount": amount,
                        "response_json": response_json,
                    }
                )

            self.message_user(request, "Successfully Checked Transactions")

    def no_task_re_verify_transactions(self, request, queryset):
        if queryset.filter(is_resolved=True).exists():
            self.message_user(request, "Error. There is a verified transaction in selected items")
        else:
            for query in queryset[:10]:
                rrn = query.rrn
                terminal_id = query.terminal_id
                amount = query.amount
                response_json = json.loads(query.payload)
                verify_trans = HorizonPayTable.new_function_to_resolve_and_settle_cashout(rrn, terminal_id, amount, response_json)

            self.message_user(request, "Successfully Checked Transactions")

    def force_mismatched_tids(self, request, queryset):
        if queryset.filter(is_resolved=True).exists():
            self.message_user(request, "Error. There is a verified transaction in selected items")
        else:
            list_of_unique_rrns = []

            for query in queryset:
                rrn = query.rrn
                if rrn not in list_of_unique_rrns:
                    from horizon_pay.tasks import accept_cashout_resolve_task

                    terminal_id = query.terminal_id
                    amount = query.amount
                    response_json = json.loads(query.payload)
                    force_resolve = True

                    accept_cashout_resolve_task.apply_async(
                        queue="resolvecashout",
                        kwargs={
                            "rrn": rrn,
                            "terminal_id": terminal_id,
                            "amount": amount,
                            "response_json": response_json,
                            "force_resolve": force_resolve,
                            "resolved_by": request.user.email
                        }
                    )

                    list_of_unique_rrns.append(rrn)

            list_of_unique_rrns = []
            self.message_user(request, "Successfully Checked Transactions")

    def no_task_force_mismatched_tids(self, request, queryset):
        if queryset.filter(is_resolved=True).exists():
            self.message_user(request, "Error. There is a verified transaction in selected items")
        else:
            list_of_unique_rrns = []

            for query in queryset[:10]:
                rrn = query.rrn
                if rrn not in list_of_unique_rrns:

                    terminal_id = query.terminal_id
                    amount = query.amount
                    response_json = json.loads(query.payload)
                    force_resolve = True

                    verify_trans = HorizonPayTable.new_function_to_resolve_and_settle_cashout(
                        rrn, terminal_id, amount, response_json, force_resolve=force_resolve, resolved_by=request.user.email
                    )
                    print(verify_trans)

                    list_of_unique_rrns.append(rrn)

            list_of_unique_rrns = []
            response = "Successfully Checked Transactions" if verify_trans else "RRN or amount incorrect in Start Cashout"

            self.message_user(request, response)

    re_verify_transactions.short_description = "Verify Transactions For Selected Items"
    no_task_re_verify_transactions.short_description = "No Celery Task Verify Transactions For Selected Items"
    force_mismatched_tids.short_description = "Resolve Mismatched TIDS"
    no_task_force_mismatched_tids.short_description = "NO TASK Resolve Mismatched TIDS"

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = super().get_readonly_fields(request, obj)

        if (obj and obj.is_resolved) or (obj and obj.as_chargeback) or (obj and obj.wrong_tid): # editing an existing object
            self.readonly_fields += ['retry_resolve', 'as_chargeback', 'wrong_tid']

        if request.user.email not in ['<EMAIL>', '<EMAIL>', '<EMAIL>']:
            self.readonly_fields += ['is_resolved']
        return readonly_fields

    resource_class = HorizonPayTableResource
    readonly_fields = [
        "payload", "retry_response", "rrn", "ip_addr", "ip_correct",
        "mti", "amount", "terminal_id", "response_code", "response_description", "pan", "stan",
        "auth_code", "transaction_time", "reversal", "merchant_id", "merchant_name", "merchant_address",
    ]

    search_fields = ['payload', 'rrn']
    list_filter = (
        ('date_created', "is_resolved", "as_chargeback", "retry_resolve", "wrong_tid", "ip_correct")
    )
    date_hierarchy = 'date_created'
    actions = [re_verify_transactions, no_task_re_verify_transactions, force_mismatched_tids, no_task_force_mismatched_tids]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def get_search_results(self, request, queryset, search_term):

        names = [x.strip() for x in search_term.split(',')]
        if len(names) == 1 and not names[0].strip():
            return queryset, True

        filtered_queryset = queryset.filter(rrn__in=names).distinct('rrn')

        print(filtered_queryset)
        return filtered_queryset, True

    # def change_view(self, request, object_id, form_url='', extra_context=None):
    #     if 'found_names' in extra_context:
    #         found_names = extra_context['found_names']
    #         if found_names:
    #             self.message_user(request, f"Found names: {', '.join(found_names)}")
    #     return super().change_view(request, object_id, form_url, extra_context)


class CardTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = CardTransactionResource
    search_fields = ['user_email', 'reference_number', 'terminal_id', 'transaction_object_id']
    list_filter = (
        ('date_created', 'status')
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TransactionOwnersResourceAdmin(ImportExportModelAdmin):
    resource_class = TransactionOwnersResource
    search_fields = ['owner_code']
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CardSignatureVerificationResourceAdmin(ImportExportModelAdmin):
    resource_class = CardSignatureVerificationResource
    search_fields = ['user_email', 'token', "tID"]
    list_filter = (
        ('is_checked', 'is_correct', 'twofa_started', 'twofa_complete', 'date_created')
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CardTransaactionRawPayloadResourceAdmin(ImportExportModelAdmin):
    search_fields = ["payload", "user__email"]
    resource_class = CardTransaactionRawPayloadResource
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CardIssuerTableResourceAdmin(ImportExportModelAdmin):
    resource_class = CardIssuerTableResource
    search_fields = ["bank",]
    list_filter = (
        ('date_created', 'last_updated')
    )
    date_hierarchy = 'date_created'

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TerminalSerialTableResourceAdmin(ImportExportModelAdmin):

    def register_tid(self, request, queryset):
        register_tid = TerminalSerialTable.reregister_tid(queryset)

        self.message_user(request, "Successfully registered TID")

    def make_tid_arca_clear(self, request, queryset):
        for query in queryset:
            query: TerminalSerialTable
            query.arca_clear = True
            query.save()

        self.message_user(request, "Successfully Made TID Arca Clear")

    register_tid.short_description = "Register TIDs"
    make_tid_arca_clear.short_description = "Make Arca Clear"

    autocomplete_fields = ['user']
    search_fields = ['user__email', 'terminal_id', 'isw_terminal_id', 'arca_terminal_id', 'terminal_serial', 'terminal_provider']
    resource_class = TerminalSerialTableResource
    list_filter = (
        ('date_assigned', 'date_created', 'terminal_provider', 'is_registered')
    )
    readonly_fields = ["date_assigned", "isw_terminal_id"]
    actions = [register_tid, make_tid_arca_clear]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class StartCashOutTranResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    search_fields = ['user__email', 'tID', 'rrn']
    resource_class = StartCashOutTranResource
    list_filter = (
        ('date_created', 'trans_complete')
    )
    # readonly_fields = ["rrn", "tID"]
    # readonly_fields = [
    #     "user", "tid", "rrn", "ip_addr", "amount_started", "trans_complete", "amount_resolved", "send_money_data",
    #     "send_money_by_card", "ledger_commission", "commission_type", "access_token", "date_created", "last_updated"
    # ]

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = super().get_readonly_fields(request, obj)

        if request.user.email not in ['<EMAIL>', '<EMAIL>']:
            readonly_fields += tuple([
                "user", "tID", "rrn", "ip_addr", "amount_started", "trans_complete", "amount_resolved", "send_money_data",
                "send_money_by_card", "ledger_commission", "commission_type", "access_token", "date_created", "last_updated"
            ])

        # if request.user.email in ['<EMAIL>']:
        # if request.user.email in ['<EMAIL>']:

        try:
            group = Group.objects.get(name='Resolve CashOut')
            if group in request.user.groups.all():
                readonly_fields = tuple(item for item in readonly_fields if item != "trans_complete")
        except Group.DoesNotExist:
            pass

        return readonly_fields

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RecordTerminalSerialResourceAdmin(ImportExportModelAdmin):
    # def save_model(self,request,form,obj,change):
    #     print('ENTERING SAVE_MODEL FUNCTION')
    #     # if not change:
    #         # if obj.assignor_user:
    #         #     pass
    #         # else:
    #     obj.assignor_user = request.user
    #     obj.save()
    #     # print "EXITING SAVE_MODEL FUNCTION"

    # def get_changeform_initial_data(self, request):
    #     return {'assignor_user': request.user}

    # def get_form(self, request, *args, **kwargs):
    #     form = super(RecordTerminalSerialResourceAdmin, self).get_form(request, *args, **kwargs)
    #     form.base_fields['collector_name'].initial = request.user.email
    #     return form

    # readonly_fields = ["assignor_user", "date_assigned"]
    readonly_fields = ["date_assigned"]
    autocomplete_fields = ['terminal']
    search_fields = ['terminal__terminal_id', 'terminal__terminal_serial', 'collector_name', 'terminal__terminal_user__email']
    resource_class = RecordTerminalSerialResource
    list_filter = (
        ('date_assigned', 'date_created')
    )
    # readonly_fields = ["rrn", "tID"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ManualRefundResourceAdmin(ImportExportModelAdmin):
    search_fields = ['rrn']
    resource_class = ManualRefundResource
    list_filter = (
        ('date_created',)
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ISWTerminalTableResourceAdmin(ImportExportModelAdmin):
    search_fields = ['isw_terminal_id']
    resource_class = ISWTerminalTableResource
    list_filter = (
        ('date_created',)
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NewCardSaveDataTableResourceAdmin(ImportExportModelAdmin):
    search_fields = ['register_tid_initial_payload', 'register_tid_response']
    resource_class = NewCardSaveDataTableResource
    list_filter = (
        ('date_created',)
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DebugTerminalResourceAdmin(ImportExportModelAdmin):
    search_fields = ['serial_no']
    resource_class = DebugTerminalResource
    list_filter = (
        ('date_created', 'is_active')
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ArcaPayTerminalTableResourceAdmin(ImportExportModelAdmin):
    search_fields = ['arca_terminal_id']
    resource_class = ArcaPayTerminalTableResource
    list_filter = (
        ('date_created',)
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(HorizonPayTable, HorizonPayTableResourceAdmin)
admin.site.register(CardTransaction, CardTransactionResourceAdmin)
admin.site.register(CardSignatureVerification, CardSignatureVerificationResourceAdmin)
admin.site.register(CardTransaactionRawPayload, CardTransaactionRawPayloadResourceAdmin)
admin.site.register(CardIssuerTable, CardIssuerTableResourceAdmin)
admin.site.register(TerminalSerialTable, TerminalSerialTableResourceAdmin)
admin.site.register(StartCashOutTran, StartCashOutTranResourceAdmin)
admin.site.register(RecordTerminalSerial, RecordTerminalSerialResourceAdmin)
admin.site.register(ManualRefund, ManualRefundResourceAdmin)
admin.site.register(ISWTerminalTable, ISWTerminalTableResourceAdmin)
admin.site.register(NewCardSaveDataTable, NewCardSaveDataTableResourceAdmin)
admin.site.register(DebugTerminal, DebugTerminalResourceAdmin)
admin.site.register(ArcaPayTerminalTable, ArcaPayTerminalTableResourceAdmin)
admin.site.register(TransactionOwners, TransactionOwnersResourceAdmin)


