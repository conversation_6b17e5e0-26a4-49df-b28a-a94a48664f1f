from datetime import datetime
from django.db.models import Q
from django.conf import settings
from django.utils.timezone import localtime

from rest_framework import status, generics
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.filters import SearchFilter
from django_filters.rest_framework import DjangoFilterBackend


from horizon_pay.helpers.helper_function import encrypt_trans_pin, format_hapticks_data, send_bank_transfer_redirect, check_terminal_id_exist
from horizon_pay.models import Card<PERSON><PERSON>uerTable, CardTransaction, HorizonPayTable, CardSignatureVerification, CardTransaactionRawPayload, StartCashOutTran, TerminalSerialTable
from horizon_pay.serializers import BeginTransactionSerializer, CardInputSerializer, CardWithdrawSerializer, Start2FASerializer, \
    HorizonPayTableSerializer, CashOutFeeSerializer, CardRawPayloadSerializer, GetDataHorizonPayTableSerializer, CheckActualDepositSerializer, \
    LimitCheckSerializerIn, TerminalSerialTableSerializer, ReversalCallbackSerializerIn, ValidateCardTransactionSerializerIn, \
    LimitCheckNewSerializerIn
from horizon_pay.tasks import accept_cashout_resolve_task
from liberty_pay.exceptions import raise_serializer_error_msg

from main.models import ConstantTable, User, SuperAgentProfile
from main.permissions import CheckAccountAvailable, CheckAdminPassword, CheckWalletAvailable, HasKYC, HasKYCLevelTwo, OTPVerified, HasTransactionPin, CanSendMoney, WhitelistPermission, CustomIsAuthenticated, CheckDynamicAuthentication

from accounts.models import CashOutChargeBand, Escrow, OtherCommissionsRecord, Transaction, WalletSystem
from accounts.filters import ForAllDateFilter

from admin_dashboard.helpers.helpers import filter_by_date_two, Paginator

from retail.models import LibertyRetailCharge

import json
import uuid

def get_true_or_false_input(value):
    return bool(int(value))


class Start2FAView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, CanSendMoney, HasKYC]
    serializer_class = Start2FASerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            signature_token = serializer.validated_data["signature_token"]

            token_verification = CardSignatureVerification.create_new_token(token=signature_token, user_email=request.user.email)
            if token_verification["status"] != "SUCCESSFUL":
                response = {
                    "error":"194",
                    "message": "invalid signature token"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            response = {
                "message": "signature validated successfully"
            }

            return Response(response, status=status.HTTP_200_OK)


        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class BeginCashOutTrans(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, CanSendMoney, HasKYC, HasKYCLevelTwo]
    serializer_class = BeginTransactionSerializer

    def post(self, request):
        user = request.user
        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid():
            amount = serializer.validated_data["amount"]
            tID = serializer.validated_data["tID"]
            send_money_data = serializer.validated_data["send_money_data"]
            ledger_commission = serializer.validated_data.get("ledger_commission")
            commission_type = serializer.validated_data.get("commission_type")
            trx_owner = serializer.validated_data.get("trx_owner")

            if ledger_commission is None:
                ledger_commission = 0


            # if request.user.terminal_id != tID:
            #     response = {
            #         "error":"988",
            #         "message": "TID Mismatch"
            #     }
            #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

            constant_table = ConstantTable.get_constant_table_instance()

            # Check global transaction limit
            if float(amount) > constant_table.global_cashout_transaction_limit:
                return Response(
                    {"status": "error", "message": "Amount cannot be greater than transaction limit"}, status=status.HTTP_400_BAD_REQUEST
                )

            if amount > 8000:
                if not constant_table.card_interswitch_regulator:
                    response = {
                        "error": "548",
                        "message": "Sorry, cannot process amount entered right now. Please try with a lesser amount"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    pass
            else:
                if not constant_table.card_nibbs_regulator:
                    response = {
                        "error": "548",
                        "message": "Sorry, cannot process amount entered right now. Please try with a higher amount"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    pass

            auth_header = request.headers.get('Authorization', '')
            token_type, _, credentials = auth_header.partition(' ')

            hashed_access_token = None

            if send_money_data:
                transaction_pin = send_money_data.get("transaction_pin")
                get_hashed_trans_pin = encrypt_trans_pin(transaction_pin)
                send_money_data["transaction_pin"] = get_hashed_trans_pin

                hashed_access_token = encrypt_trans_pin(credentials)

            # Get IP ADDRESS
            address = request.META.get('HTTP_X_FORWARDED_FOR')
            if address:
                ip_addr = address.split(',')[-1].strip()
            else:
                ip_addr = request.META.get('REMOTE_ADDR')

            get_terminal_fk: TerminalSerialTable = user.terminal_serial_set.last()
            if not get_terminal_fk:
                response = {
                    "status": "error",
                    "message": "No TID found",
                }

                return Response(response, status=status.HTTP_200_OK)

            # Check Transaction Amount Band to determine TID to generate
            interswitch_amount = 9100
            # tID = get_terminal_fk.isw_terminal_id if amount >= interswitch_amount  else get_terminal_fk.terminal_id
            # tID = get_terminal_fk.terminal_id if tID is None else tID
            # tID = get_terminal_fk.terminal_id

            tID = StartCashOutTran.dynamic_start_tid(amount=amount, get_terminal_fk=get_terminal_fk)

            get_rrn = StartCashOutTran.create_trans_rrn(
                user=user,
                tID=tID,
                amount_started=amount,
                send_money_data=send_money_data,
                ip_addr=ip_addr,
                ledger_commission=ledger_commission,
                commission_type=commission_type,
                access_token=hashed_access_token,
                trx_owner=trx_owner,
                terminal_serial=get_terminal_fk,
            )

            response = {
                "message": "rrn generated successfully",
                "rrn": get_rrn
            }
            return Response(response, status=status.HTTP_200_OK)


        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)



class HorizonPayCallBackAPIView(APIView):
    permission_classes = [WhitelistPermission]

    def post(self, request):
        response = request.data

        # Get IP ADDRESS
        address = request.META.get('HTTP_X_FORWARDED_FOR')
        if address:
            ip_addr = address.split(',')[-1].strip()
        else:
            ip_addr = request.META.get('REMOTE_ADDR')

        hapticks_resp = HorizonPayTable.objects.create(
            payload = json.dumps(response),
            ip_addr = ip_addr
        )

        response_json = json.loads(hapticks_resp.payload)
        terminal_id = response_json.get("terminalId")
        rrn = response_json.get("rrn")
        amount = response_json.get("amount")

        hapticks_resp.rrn = rrn
        hapticks_resp.amount = amount
        hapticks_resp.terminal_id = terminal_id
        hapticks_resp.save()


        # if Transaction.objects.filter(unique_reference=rrn).exists():
        #     return_resp = {
        #         "status":"false",
        #         "message": "rrn exists"
        #     }
        try:
            Transaction.objects.get(unique_reference=rrn)

            return_resp = {
                "status":"false",
                "message": "rrn exists"
            }

        except Transaction.DoesNotExist:

            if ConstantTable.get_constant_table_instance().use_task_for_cash_out_resolve:

                accept_cashout_resolve_task.apply_async(
                    queue="resolvecashout",
                    kwargs={
                        "rrn": rrn,
                        "terminal_id": terminal_id,
                        "amount": amount,
                        "response_json": response_json
                    }
                )

            else:
                accept_cashout_resolve_task(
                    rrn=rrn,
                    terminal_id=terminal_id,
                    amount=amount,
                    response_json=response_json
                )



            # initialized_trans = StartCashOutTran.objects.filter(rrn=rrn, trans_complete=False, tID=terminal_id, amount_started=amount).last()
            # tID_user = User.objects.filter(terminal_id=terminal_id).last()
            # if initialized_trans and tID_user:

            #     HorizonPayTable.resolve_and_settle_funds(
            #         user = tID_user,
            #         data = response_json,
            #         initialized_trans_data = initialized_trans
            #     )

            return_resp = {
                "status":"true",
                "message": "response received"
            }

        except:

            return_resp = {
                "status":"false",
                "message": "rrn exists"
            }


        return Response(
            return_resp,
            status=status.HTTP_200_OK
        )


class LibertyPayPOSCallBackAPIView(APIView):
    serializer_class = CardWithdrawSerializer
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, CanSendMoney, HasKYC]

    def post(self, request):
        request_data = request.data

        admin_password = request_data.get("admin_password")
        user_by_admin = request_data.get("user_by_admin")

        if admin_password and not user_by_admin or user_by_admin and not admin_password:
            response = {
                "error":"2500",
                "message": "Enter either admin password or user by admin"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


        if admin_password and user_by_admin:
            check_user_as_admin = User.objects.filter(email=user_by_admin).last()

            if admin_password != f"{settings.EMEKA_ADMIN_PASSWORD}" or not check_user_as_admin:
                response = {
                    "error":"2500",
                    "message": "Invalid Admin Password. Remove Key or try again OR Email does not exist"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:
                request_user = check_user_as_admin
        else:

            request_user = request.user

#####################################################################################################

        first_card_dump_resp = request_data


        if first_card_dump_resp["send_money_by_card"].get("data"):
            removed_transaction_pin = first_card_dump_resp["send_money_by_card"]["data"].pop("transaction_pin")
        else:
            pass



        save_card_request_data = CardTransaactionRawPayload.objects.create(
            user=request_user,
            payload = json.dumps(first_card_dump_resp)
        )


        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)


        refNum = serializer.validated_data["refNum"]
        resultCode = serializer.validated_data["resultCode"]
        mID = serializer.validated_data["mID"]
        tID = serializer.validated_data["tID"]
        hostRespCode = serializer.validated_data["hostRespCode"]
        hostRespMsg = serializer.validated_data["hostRespMsg"]
        merchantName = serializer.validated_data["merchantName"]
        merchantAddress = serializer.validated_data["merchantAddress"]
        acquirerName = serializer.validated_data["acquirerName"]
        appLabel = serializer.validated_data["appLabel"]
        pAN = serializer.validated_data["pAN"]
        authCode = serializer.validated_data["authCode"]
        expireDate = serializer.validated_data["expireDate"]
        holderName = serializer.validated_data["holderName"]
        ptspName = serializer.validated_data["ptspName"]
        ptspContact = serializer.validated_data["ptspContact"]
        deviceSN = serializer.validated_data["deviceSN"]
        baseAppVer = serializer.validated_data["baseAppVer"]
        traceNum = serializer.validated_data["traceNum"]
        stan = serializer.validated_data.get("stan", None)
        amount = float(serializer.validated_data["amount"])
        timestamp = serializer.validated_data["timestamp"]
        ledger_commission = serializer.validated_data.get("ledger_commission")
        commission_type = serializer.validated_data.get("commission_type")
        signature_token = serializer.validated_data.get("signature_token")





        token_verification = CardSignatureVerification.verify_signature_token(token=signature_token, tID=tID, amount=amount, user_email=request_user.email)

        if token_verification["status"] != "SUCCESSFUL":
            response = {
                "error":"194",
                "message": "invalid signature token"
            }

            return Response(response, status=status.HTTP_400_BAD_REQUEST)



        # send_money_by_card = get_true_or_false_input(value=request_data.get("send_money_by_card")["is_send_money_trans"])
        # send_money_by_card_data = request_data.get("send_money_by_card")["data"]

        # auth_header = request.headers.get('Authorization', '')
        # token_type, _, credentials = auth_header.partition(' ')


        # user_access_token = credentials


        # type_of_user = request_user.type_of_user

        # if request_user.type_of_user == "AGENT":
        #     commission_obj = CashOutChargeBand.calculate_cash_out_agent_fees(amount)
        #     liberty_commission = commission_obj["total_charge"]
        #     # liberty_commission = ConstantTable.calculate_card_withdraw_transaction_charge_agent(amount=amount)

        # else:
        #     commission_obj = CashOutChargeBand.calculate_cash_out_agent_fees(amount)
        #     liberty_commission = commission_obj["total_charge"]
        #     # liberty_commission = ConstantTable.calculate_card_withdraw_transaction_charge_merchant(amount=amount)


        # resolvable_amount = amount - liberty_commission

        # if refNum:

        #     liberty_reference = f"LP-CARD_INW-{str(uuid.uuid4())}"

        #     check_user = User.objects.filter(terminal_id = tID).first()

        #     if not check_user:
        #         save_card_request_data.fail_reason = "User and TID user mismatch"
        #         save_card_request_data.save()

        #         return Response(
        #             {
        #                 "error":"739",
        #                 "message": "invalid response received. This user does not have this TID assigned"
        #             },
        #             status=status.HTTP_400_BAD_REQUEST
        #         )

        #     else:
        #         if not check_user == request_user:
        #             save_card_request_data.fail_reason = "User and TID user mismatch"
        #             save_card_request_data.save()

        #             return Response(
        #                 {
        #                     "error":"738",
        #                     "message": "invalid response received. TID mismatch"
        #                 },
        #                 status=status.HTTP_400_BAD_REQUEST
        #             )

        #         else:

        #             user = check_user
        #             get_current_provider = ConstantTable.default_account_provider()
        #             print(get_current_provider)
        #             print(get_current_provider)
        #             print(get_current_provider)
        #             print(get_current_provider)
        #             print(get_current_provider)

        #             check_wallet = WalletSystem.objects.filter(Q(user=user) & Q(wallet_type="COLLECTION")).last()

        #             print("I have a wallet")

        #             if check_wallet:
        #                 wallet_id = check_wallet.wallet_id
        #                 wallet_type = check_wallet.wallet_type

        #                 user_balance_before = check_wallet.available_balance
        #                 user_balance_after = WalletSystem.get_balance_after(
        #                     user = user,
        #                     balance_before=user_balance_before,
        #                     total_amount=resolvable_amount,
        #                     is_credit=True
        #                 )

        #             else:
        #                 wallet_id = None
        #                 wallet_type = None

        #                 user_balance_before = 0.00
        #                 user_balance_after = 0.00

        #     # else:

        #     #     user = None
        #     #     wallet_id = None
        #     #     wallet_type = None
        #     #     get_current_provider = None

        #     #     user_balance_before = 0.00
        #     #     user_balance_after = 0.00

        #     # print(user)
        #     # print(wallet_id)

        #     check_if_transaction_exist = Transaction.objects.filter(unique_reference=refNum).exists()
        #     if not check_if_transaction_exist:

        #         print("I CAME HEREEEE")



        #         if send_money_by_card:
        #             # transaction_type = "CARD_TRANSACTION_FUND_TRANSFER"
        #             transaction_type = "CARD_TRANSACTION_FUND"
        #         else:
        #             transaction_type = "CARD_TRANSACTION_FUND"


        #         escrow_instance = Escrow.objects.create(
        #             user=user,
        #             transfer_type="SEND_COMMISSION"
        #         )

        #         # Get IP ADDRESS
        #         address = request.META.get('HTTP_X_FORWARDED_FOR')
        #         if address:
        #             ip_addr = address.split(',')[-1].strip()
        #         else:
        #             ip_addr = request.META.get('REMOTE_ADDR')


        #         transaction_instance = Transaction.objects.create(
        #             user = user,
        #             wallet_id = wallet_id,
        #             wallet_type = wallet_type,
        #             account_provider = get_current_provider,
        #             transaction_type = transaction_type,
        #             amount = amount,
        #             balance_before = user_balance_before,
        #             balance_after = user_balance_after,
        #             liberty_commission = liberty_commission,
        #             total_amount_received = amount,
        #             escrow_id = escrow_instance.escrow_id,
        #             status = "PENDING",
        #             provider_status = hostRespMsg,
        #             liberty_reference = liberty_reference,
        #             ip_addr=ip_addr,
        #             unique_reference = refNum,
        #             merchant_id = mID,
        #             terminal_id = tID,
        #             type_of_user = type_of_user,
        #             transaction_mode = transaction_type,
        #             payload = str(first_card_dump_resp)
        #         )

        #         get_sales_rep = OtherCommissionsRecord.check_if_sales_rep_exists(downline_user=user)
        #         if get_sales_rep is None:
        #             sales_rep = True
        #             final_liberty_rev = commission_obj["liberty_profit"] + commission_obj["ro_profit"]
        #         else:
        #             sales_rep = False
        #             final_liberty_rev = commission_obj["liberty_profit"]



        #         card_transaction_instance = CardTransaction.objects.create(
        #             user_email = user.email,
        #             transaction = transaction_instance,
        #             transaction_object_id = transaction_instance.transaction_id,
        #             reference_number = refNum,
        #             resultCode = resultCode,
        #             stan = stan,
        #             amount = amount,
        #             liberty_commission = liberty_commission,
        #             sales_rep = sales_rep,
        #             final_liberty_rev = final_liberty_rev,
        #             liberty_profit = commission_obj["liberty_profit"],
        #             ro_profit = commission_obj["ro_profit"],
        #             agent_profit = commission_obj["agent_profit"],
        #             merchant_id = mID,
        #             terminal_id = tID,
        #             type_of_user = type_of_user,
        #             host_resp_code = hostRespCode,
        #             host_resp_msg = hostRespMsg,
        #             auth_code = authCode,
        #             merchant_name = merchantName,
        #             merchant_address = merchantAddress,
        #             acquirer_name = acquirerName,
        #             timestamp = timestamp,
        #             app_label = appLabel,
        #             pan_number = pAN,
        #             expire_date = expireDate,
        #             holder_name = holderName,
        #             ptsp_name = ptspName,
        #             ptsp_contact = ptspContact,
        #             device_serial_number = deviceSN,
        #             base_app_ver = baseAppVer,
        #             trace_num = traceNum,
        #             ip_addr = ip_addr,
        #             payload = str(first_card_dump_resp)
        #         )

        #     else:
        #         transaction_instance = Transaction.objects.filter(unique_reference=refNum).last()

        #         card_transaction_instance = CardTransaction.objects.filter(Q(transaction=transaction_instance) | Q(reference_number=refNum)).first()

        #         # If Transaction exist and is not successful yet

        #     print("I AM CLOSE TO VERY SUCCESSFUL")
        #     if transaction_instance and transaction_instance.status != "SUCCESSFUL":
        #                 # update and change to success

        #         if resultCode == 0 and "approved" in hostRespMsg.casefold():

        #             print("I AM VERY SUCCESSFUL")
        #             print("I AM VERY SUCCESSFUL")

        #             # Manage Ledger
        #             if ledger_commission:
        #                 if commission_type is None:
        #                     commission_type = "CASH"
        #                 else:
        #                     commission_type = commission_type

        #             else:
        #                 pass

        #             # update and change to success
        #             transaction_instance.status = "SUCCESSFUL"
        #             card_transaction_instance.status = "SUCCESSFUL"

        #             transaction_instance.save()
        #             card_transaction_instance.save()

        #             # SETTLE MONEY
        #             if check_user and check_wallet:
        #                 CardTransaction.settle_money_function(
        #                     user=user,
        #                     amount=amount,
        #                     wallet_id=wallet_id,
        #                     wallet_type=wallet_type,
        #                     liberty_commission=liberty_commission,
        #                     transaction_instance_id=transaction_instance.transaction_id,
        #                     from_provider_type=get_current_provider,
        #                     commission_obj = commission_obj,
        #                     rrn=refNum,
        #                     escrow_id = escrow_instance.escrow_id
        #                 )

        #                 # print(request_data)
        #                 if send_money_by_card:
        #                     send_money_by_card_data["transaction_pin"] = removed_transaction_pin
        #                     send_money = send_bank_transfer_redirect(
        #                         send_money_by_card_data,
        #                         user_access_token
        #                     )
        #                 else:
        #                     pass

        #             else:
        #                 pass

        #         else:
        #             # update its details
        #             transaction_instance.status = "FAILED"
        #             transaction_instance.balance_after = user_balance_before

        #             card_transaction_instance.status = "FAILED"


        #         transaction_instance.save()
        #         card_transaction_instance.save()


        #     else:
        #         pass



        return Response(
            {"status":"true", "message": "response received"},
            status=status.HTTP_200_OK
        )


class HorizonCheckAvailabilityAPIView(APIView):

    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC, HasKYCLevelTwo, CheckWalletAvailable, CheckAccountAvailable]

    def get(self, request, method):

        check_if_terminal_id_exist = check_terminal_id_exist(request.user)

        if not check_if_terminal_id_exist:
            response = {
                "error": "746",
                "message": "You do not have a Terminal ID assigned to your profile"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


        if method == "SEND_BY_CARD":
            if ConstantTable.get_constant_table_instance().send_money_regulator is False:
                response = {
                    "error": "743",
                    "message": "Service Unavailable"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:
                response = {
                    "message": "Success! Please proceed"
                }
                return Response(response, status=status.HTTP_200_OK)


        elif method == "NORMAL_WITHDRAW":
            if ConstantTable.get_constant_table_instance().card_withdraw_regulator is False:
                response = {
                    "error": "743",
                    "message": "Service Unavailable"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:
                response = {
                    "message": "Success! Please proceed"
                }
                return Response(response, status=status.HTTP_200_OK)

        else:
            response = {
                "error": "749",
                "message": "method is incorrect"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


class CardIssuerCashOutView(APIView):
    permission_classes = [CustomIsAuthenticated]
    # serializer_class = CardIssuerSerializer

    def get(self, request):
        entry = request.query_params.get('entry')

        if entry is None:
            response = {
                "error": "549",
                "message": "No entry attached"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        if entry not in ["CASH_OUT", "TRANSFER"]:
            response = {
                "error": "550",
                "message": "entry must be either 'CASH_OUT' or 'TRANSFER'"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)


        get_data = CardIssuerTable.objects.filter(performance_type=entry, bank__isnull=False)

        if get_data is None:
            response = {
                "error": "550",
                "message": "No Performance Found"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)
        else:
            bank_names = get_data.values_list('bank', flat=True).distinct()


            if entry == "TRANSFER":
                banks = [{
                    'bank': data,
                    'performance': {
                        'Deposit': None,
                        # 'Visa': None,
                        # 'Verve': None,
                    }
                } for data in bank_names]

            else:
                banks = [{
                    'bank': data,
                    'performance': {
                        'Mastercard': None,
                        'Visa': None,
                        'Verve': None,
                    }
                } for data in bank_names]

            index, bank_index = 0, {}
            for i in banks:
                bank_index[i['bank']] = index
                index += 1
            for data in get_data:
                ind = bank_index[data.bank]
                if entry != "TRANSFER":
                    banks[ind]['performance'][data.brand] = data.performance
                else:
                    banks[ind]['performance']["Deposit"] = data.performance


            # for data in get_data:
            #     if data.bank not in response.keys():
            #         response[data.bank] = {
            #             data.brand: data.front_view_performance
            #         }
            #     else:
            #         response[data.bank][data.brand] = data.front_view_performance

            return Response({'banks': banks}, status=status.HTTP_200_OK)


class HorizonPayTableListView(generics.ListAPIView):
    serializer_class = HorizonPayTableSerializer
    queryset = HorizonPayTable.objects.all()

    def get(self, request):
        date_filter = filter_by_date_two(request=request, user=User, datetime=datetime)
        start_date = date_filter.get("start_date")
        end_date = date_filter.get("end_date")

        horizon_pay_table_qs = self.queryset.filter(date_created__range=[start_date, end_date])
        horizon_pay_table_qs = Paginator.paginate(request=request, queryset=horizon_pay_table_qs)

        serializer = self.serializer_class(horizon_pay_table_qs, many=True)

        response = {
            "data": serializer.data,
            "count": len(serializer.data)
        }

        return Response(response, status=status.HTTP_200_OK)


class CardDataJSONFormatterAPIView(APIView):
    # permission_classes = [CustomIsAuthenticated]
    input_serializer = CardInputSerializer

    def post(self, request):
        data = request.data

        serializer = self.input_serializer(data=data)
        if serializer.is_valid():

            response = format_hapticks_data(serializer.validated_data)

            return Response(response, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SecondLibertyPayPOSCallBackAPIView(APIView):
    serializer_class = CardWithdrawSerializer
    permission_classes = [CheckAdminPassword]

    def post(self, request):
        request_data = request.data

        admin_password = request_data.get("admin_password")
        user_by_admin = request_data.get("user_by_admin")

        if admin_password and not user_by_admin or user_by_admin and not admin_password:
            response = {
                "error":"2500",
                "message": "Enter either admin password or user by admin"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


        if admin_password and user_by_admin:
            check_user_as_admin = User.objects.filter(email=user_by_admin).last()

            if admin_password != f"{settings.EMEKA_ADMIN_PASSWORD}" or not check_user_as_admin:
                response = {
                    "error":"2500",
                    "message": "Invalid Admin Password. Remove Key or try again OR Email does not exist"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:
                request_user = check_user_as_admin
        else:

            request_user = request.user

#####################################################################################################

        first_card_dump_resp = request_data

        if first_card_dump_resp["send_money_by_card"].get("data"):
            removed_transaction_pin = first_card_dump_resp["send_money_by_card"]["data"].pop("transaction_pin")
        else:
            pass



        save_card_request_data = CardTransaactionRawPayload.objects.create(
            user=request_user,
            payload = json.dumps(first_card_dump_resp)
        )


        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)


        refNum = serializer.validated_data["refNum"]
        resultCode = serializer.validated_data["resultCode"]
        mID = serializer.validated_data["mID"]
        tID = serializer.validated_data["tID"]
        hostRespCode = serializer.validated_data["hostRespCode"]
        hostRespMsg = serializer.validated_data["hostRespMsg"]
        merchantName = serializer.validated_data["merchantName"]
        merchantAddress = serializer.validated_data["merchantAddress"]
        acquirerName = serializer.validated_data["acquirerName"]
        appLabel = serializer.validated_data["appLabel"]
        pAN = serializer.validated_data["pAN"]
        authCode = serializer.validated_data["authCode"]
        expireDate = serializer.validated_data["expireDate"]
        holderName = serializer.validated_data["holderName"]
        ptspName = serializer.validated_data["ptspName"]
        ptspContact = serializer.validated_data["ptspContact"]
        deviceSN = serializer.validated_data["deviceSN"]
        baseAppVer = serializer.validated_data["baseAppVer"]
        traceNum = serializer.validated_data["traceNum"]
        stan = serializer.validated_data.get("stan", None)
        amount = float(serializer.validated_data["amount"])
        timestamp = serializer.validated_data["timestamp"]
        ledger_commission = serializer.validated_data.get("ledger_commission")
        commission_type = serializer.validated_data.get("commission_type")
        signature_token = serializer.validated_data.get("signature_token")



        token_verification = CardSignatureVerification.verify_signature_token(token=signature_token, tID=tID, amount=amount, user_email=request_user.email)

        if token_verification["status"] != "SUCCESSFUL":
            response = {
                "error":"194",
                "message": "invalid signature token"
            }

            return Response(response, status=status.HTTP_400_BAD_REQUEST)



        send_money_by_card = get_true_or_false_input(value=request_data.get("send_money_by_card")["is_send_money_trans"])
        send_money_by_card_data = request_data.get("send_money_by_card")["data"]

        auth_header = request.headers.get('Authorization', '')
        token_type, _, credentials = auth_header.partition(' ')


        user_access_token = credentials


        type_of_user = request_user.type_of_user

        if request_user.type_of_user in ["AGENT", "LOTTO_AGENT", "LIBERTY_RETAIL"]:
            commission_obj = CashOutChargeBand.calculate_cash_out_agent_fees(amount, user=request_user)
            liberty_commission = commission_obj["total_charge"]
            # liberty_commission = ConstantTable.calculate_card_withdraw_transaction_charge_agent(amount=amount)

        else:
            commission_obj = CashOutChargeBand.calculate_cash_out_agent_fees(amount, user=request_user)
            liberty_commission = commission_obj["total_charge"]
            # liberty_commission = ConstantTable.calculate_card_withdraw_transaction_charge_merchant(amount=amount)


        resolvable_amount = amount - liberty_commission

        if refNum:

            liberty_reference = Transaction.create_liberty_reference(suffix="LP-CARD_INW")

            check_user = User.objects.filter(terminal_id = tID).first()

            if not check_user:
                save_card_request_data.fail_reason = "User and TID user mismatch"
                save_card_request_data.save()

                return Response(
                    {
                        "error":"739",
                        "message": "invalid response received. This user does not have this TID assigned"
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

            else:
                if not check_user == request_user:
                    save_card_request_data.fail_reason = "User and TID user mismatch"
                    save_card_request_data.save()

                    return Response(
                        {
                            "error":"738",
                            "message": "invalid response received. TID mismatch"
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

                else:

                    user = check_user
                    get_current_provider = ConstantTable.default_account_provider()


                    check_wallet = WalletSystem.objects.filter(Q(user=user) & Q(wallet_type="COLLECTION")).last()

                    if check_wallet:
                        wallet_id = check_wallet.wallet_id
                        wallet_type = check_wallet.wallet_type

                        user_balance_before = check_wallet.available_balance
                        user_balance_after = WalletSystem.get_balance_after(
                            user = user,
                            balance_before=user_balance_before,
                            total_amount=resolvable_amount,
                            is_credit=True
                        )

                    else:
                        wallet_id = None
                        wallet_type = None

                        user_balance_before = 0.00
                        user_balance_after = 0.00

            check_if_transaction_exist = Transaction.objects.filter(unique_reference=refNum).exists()
            if not check_if_transaction_exist:


                if send_money_by_card:
                    # transaction_type = "CARD_TRANSACTION_FUND_TRANSFER"
                    transaction_type = "CARD_TRANSACTION_FUND"
                else:
                    transaction_type = "CARD_TRANSACTION_FUND"


                escrow_instance = Escrow.objects.create(
                    user=user,
                    transfer_type="SEND_COMMISSION"
                )

                # Get IP ADDRESS
                address = request.META.get('HTTP_X_FORWARDED_FOR')
                if address:
                    ip_addr = address.split(',')[-1].strip()
                else:
                    ip_addr = request.META.get('REMOTE_ADDR')


                transaction_instance = Transaction.objects.create(
                    user = user,
                    wallet_id = wallet_id,
                    wallet_type = wallet_type,
                    account_provider = get_current_provider,
                    transaction_type = transaction_type,
                    amount = amount,
                    balance_before = user_balance_before,
                    balance_after = user_balance_after,
                    liberty_commission = liberty_commission,
                    total_amount_received = amount,
                    escrow_id = escrow_instance.escrow_id,
                    status = "PENDING",
                    provider_status = hostRespMsg,
                    liberty_reference = liberty_reference,
                    ip_addr=ip_addr,
                    unique_reference = refNum,
                    terminal_id = tID,
                    type_of_user = type_of_user,
                    transaction_mode = transaction_type,
                    payload = str(first_card_dump_resp)
                )

                get_sales_rep = OtherCommissionsRecord.check_if_sales_rep_exists(downline_user=user)
                if get_sales_rep is None:
                    sales_rep = True
                    final_liberty_rev = commission_obj["liberty_profit"] + commission_obj["ro_profit"]
                else:
                    sales_rep = False
                    final_liberty_rev = commission_obj["liberty_profit"]



                card_transaction_instance = CardTransaction.objects.create(
                    user_email = user.email,
                    transaction = transaction_instance,
                    transaction_object_id = transaction_instance.transaction_id,
                    reference_number = refNum,
                    resultCode = resultCode,
                    stan = stan,
                    amount = amount,
                    liberty_commission = liberty_commission,
                    sales_rep = sales_rep,
                    final_liberty_rev = final_liberty_rev,
                    liberty_profit = commission_obj["liberty_profit"],
                    ro_profit = commission_obj["ro_profit"],
                    agent_profit = commission_obj["agent_profit"],
                    merchant_id = mID,
                    terminal_id = tID,
                    type_of_user = type_of_user,
                    host_resp_code = hostRespCode,
                    host_resp_msg = hostRespMsg,
                    auth_code = authCode,
                    merchant_name = merchantName,
                    merchant_address = merchantAddress,
                    acquirer_name = acquirerName,
                    timestamp = timestamp,
                    app_label = appLabel,
                    pan_number = pAN,
                    expire_date = expireDate,
                    holder_name = holderName,
                    ptsp_name = ptspName,
                    ptsp_contact = ptspContact,
                    device_serial_number = deviceSN,
                    base_app_ver = baseAppVer,
                    trace_num = traceNum,
                    ip_addr = ip_addr,
                    payload = str(first_card_dump_resp)
                )

            else:
                transaction_instance = Transaction.objects.filter(unique_reference=refNum).last()

                card_transaction_instance = CardTransaction.objects.filter(Q(transaction=transaction_instance) | Q(reference_number=refNum)).first()

                # If Transaction exist and is not successful yet

            if transaction_instance and transaction_instance.status != "SUCCESSFUL":
                        # update and change to success

                if resultCode == 0 and "approved" in hostRespMsg.casefold():

                    # Manage Ledger
                    if ledger_commission:
                        if commission_type is None:
                            commission_type = "CASH"
                        else:
                            commission_type = commission_type

                    else:
                        pass

                    # update and change to success
                    transaction_instance.status = "SUCCESSFUL"
                    card_transaction_instance.status = "SUCCESSFUL"

                    transaction_instance.save()
                    card_transaction_instance.save()

                    # SETTLE MONEY
                    if check_user and check_wallet:
                        CardTransaction.settle_money_function(
                            user=user,
                            amount=amount,
                            wallet_id=wallet_id,
                            wallet_type=wallet_type,
                            liberty_commission=liberty_commission,
                            transaction_instance_id=transaction_instance.transaction_id,
                            from_provider_type=get_current_provider,
                            commission_obj = commission_obj,
                            rrn=refNum,
                            escrow_id = escrow_instance.escrow_id
                        )

                        if send_money_by_card:
                            send_money_by_card_data["transaction_pin"] = removed_transaction_pin
                            send_money = send_bank_transfer_redirect(
                                send_money_by_card_data,
                                user_access_token
                            )
                        else:
                            pass

                    else:
                        pass

                else:
                    # update its details
                    transaction_instance.status = "FAILED"
                    transaction_instance.balance_after = user_balance_before

                    card_transaction_instance.status = "FAILED"


                transaction_instance.save()
                card_transaction_instance.save()


            else:
                pass



            return Response(
                {"status":"true", "message": "response received"},
                status=status.HTTP_200_OK
            )

        else:

            return Response(
                {"status":"false", "message": "invalid response received. No ref"},
                status=status.HTTP_400_BAD_REQUEST
            )





class CalculateCashOutFeeAPIView(APIView):
    # permission_classes = [CustomIsAuthenticated]
    serializer_class = CashOutFeeSerializer

    def post(self, request):
        data = request.data

        serializer = self.serializer_class(data=data)
        if serializer.is_valid():
            user_email = serializer.validated_data["user_email"]
            amount = float(serializer.validated_data["amount"])

            user = User.objects.filter(email=user_email).last()
            if not user:
                response = {
                    "error": "408",
                    "message": "User with email does not exist"
                }

                return Response(response, status=status.HTTP_200_OK)


            if user.type_of_user in ["AGENT", "LOTTO_AGENT", "LIBERTY_RETAIL"]:
                commission_obj = CashOutChargeBand.calculate_cash_out_agent_fees(amount, user=user)
                liberty_commission = commission_obj["total_charge"]

            elif user.type_of_user == "MERCHANT":
                commission_obj = CashOutChargeBand.calculate_cash_out_merchant_fees(amount)
                liberty_commission = commission_obj["total_charge"]

            else:
                commission_obj = CashOutChargeBand.calculate_cash_out_agent_fees(amount, user=user)
                liberty_commission = commission_obj["total_charge"]


            amount_to_settle = amount - liberty_commission
            response = {
                "charge": liberty_commission,
                "amount_to_settle": amount_to_settle,
                "type_of_user": user.type_of_user
            }

            return Response(response, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)



class CheckActualDepositFeeAPIView(APIView):
    # permission_classes = [CustomIsAuthenticated]
    serializer_class = CheckActualDepositSerializer

    def post(self, request):
        data = request.data

        serializer = self.serializer_class(data=data)
        if serializer.is_valid():
            amount = float(serializer.validated_data["amount"])

            get_fee = LibertyRetailCharge.objects.filter(transaction_type="CARD_TRANSACTION_FUND", upper_limit__gte=amount, lower_limit__lte=amount).last()

            if get_fee:
                charge = get_fee.charge_value
            else:
                charge = 0


            response = {
                "charge": charge
            }

            return Response(response, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)




class CashOutTransactionHistoryAPIView(generics.ListAPIView):
    permission_classes = [CheckDynamicAuthentication]
    raw_pay_serializer_class = CardRawPayloadSerializer
    notification_pay_serializer_class = GetDataHorizonPayTableSerializer
    search_fields = ['payload']
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = ForAllDateFilter

    def get_serializer_class(self):
        trans_type = self.request.query_params.get("trans_type")
        print(trans_type)
        if trans_type == "RAW":
            return self.raw_pay_serializer_class

        else:
            return self.notification_pay_serializer_class




    def get_queryset(self):

        trans_type = self.request.query_params.get("trans_type")

        search_by = self.request.query_params.get("search_by")


        if trans_type == "RAW":
            if search_by is not None:
                return CardTransaactionRawPayload.objects.filter(payload__icontains=search_by).order_by("-date_created")
            else:
                return CardTransaactionRawPayload.objects.order_by("-date_created")

        elif trans_type == "EVENT":
            if search_by is not None:
                return HorizonPayTable.objects.filter(payload__icontains=search_by).order_by("-date_created")
            else:
                return HorizonPayTable.objects.order_by("-date_created")
        else:
            return HorizonPayTable.objects.none()


class LimitCheckAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, CanSendMoney, HasKYC, HasKYCLevelTwo]

    def post(self, request):
        """
        Ensure that all agent banking terminals are set to a daily maximum transaction cash-out limit of N100,000.00 per customer
        """
        serializer = LimitCheckSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class LimitCheckNewAPIView(APIView):
    permission_classes = []

    def post(self, request):
        """
        Ensure that all agent banking terminals are set to a daily maximum transaction cash-out limit of N100,000.00 per customer
        """
        api_key = request.headers.get("X-API-KEY")
        if not api_key or api_key != settings.LIB_PAY_API_KEY:
            return Response({"status": "error", "message": "You do not have permission to access this resource"}, status=status.HTTP_403_FORBIDDEN)
        serializer = LimitCheckNewSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)
    

class VerifyTerminalDetailsAPIView(APIView):
    def post(self, request):
        api_key = request.headers.get("X-API-KEY")

        # Validate API Key
        if not api_key or api_key != settings.LIB_PAY_API_KEY:
            return Response(
                {"success": False, "error": "Invalid or missing API key."}, status=403
            )
        
        email = request.data.get("email")
        serial_number = request.data.get("terminal_serial_number")

        if not email or not serial_number:
            return Response(
                {"success": False, "error": "Email and serial number are required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if terminal exists
        try:
            terminal = TerminalSerialTable.objects.get(terminal_serial=serial_number)
        except TerminalSerialTable.DoesNotExist:
            return Response(
                {"success": False, "error": "Terminal not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Check if terminal is already assigned
        if terminal.user is not None:
            return Response(
                {"success": False, "error": "Terminal is already assigned to another user."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if user exists
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response(
                {"success": False, "error": "User not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

        return Response(
            {"success": True, "message": "Verification successful. You can proceed with registration."},
            status=status.HTTP_200_OK,
        )



class AutoRegisterTerminalAPIView(APIView):
    def post(self, request):
        api_key = request.headers.get("X-API-KEY")

        # Validate API Key
        if not api_key or api_key != settings.LIB_PAY_API_KEY:
            return Response(
                {"success": False, "error": "Invalid or missing API key."}, status=403
            )

        email = request.data.get("email")
        serial_number = request.data.get("terminal_serial_number")

        if not email or not serial_number:
            return Response(
                {"success": False, "error": "Email and serial number are required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Retrieve the terminal
        try:
            terminal = TerminalSerialTable.objects.get(terminal_serial=serial_number)
        except TerminalSerialTable.DoesNotExist:
            return Response(
                {"success": False, "error": "Terminal not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Check if the terminal is already assigned
        if terminal.user is not None:
            return Response(
                {"success": False, "error": "Terminal is already assigned to another user."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Retrieve the user
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response(
                {"success": False, "error": "User not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Assign the user to the terminal
        terminal.user = user
        terminal.is_registered = True
        terminal.type_of_user = "MERCHANT"
        terminal.save()

        # Serialize terminal details
        serializer = TerminalSerialTableSerializer(terminal)

        return Response(
            {
                "success": True,
                "message": "User successfully registered to terminal.",
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

class CheckCardTransactionStatusAPIView(APIView):
    def get(self, request):
        # Extract API Key from headers
        api_key = request.headers.get("X-API-KEY")

        # Validate API Key
        if not api_key or api_key != settings.LIB_PAY_API_KEY:
            return Response({"success": False, "error": "Invalid or missing API key."}, status=status.HTTP_403_FORBIDDEN)

        # Get RRN from query parameters
        rrn = request.GET.get("rrn")
        if not rrn:
            return Response({"success": False, "error": "RRN is required."}, status=status.HTTP_400_BAD_REQUEST)

        # Query the HorizonPayTable
        transaction = HorizonPayTable.objects.filter(rrn=rrn).first()
        if not transaction:
            return Response({"success": False, "error": "Transaction not found."}, status=status.HTTP_404_NOT_FOUND)

        # Check if resolved
        if transaction.is_resolved:
            formatted_time = localtime(transaction.last_updated).strftime("%b %d, %Y at %I:%M %p")
            return Response({
                "success": True,
                "is_resolved": True,
                "message": f"Agent account was credited on {formatted_time}.",
            }, status=status.HTTP_200_OK)

        return Response({
            "success": True,
            "is_resolved": False,
        }, status=status.HTTP_200_OK)
    


class UpdateTerminalUserTypeAPIView(APIView):
    """
    Endpoint to update the type_of_user field in the terminal serial table.
    Previous requirement was to set it as "Merchant".
    New requirement is to set it as "Lotto agent" instead of "Merchant".
    """
    def post(self, request):
        # Extract API Key from headers
        api_key = request.headers.get("X-API-KEY")

        # Validate API Key
        if not api_key or api_key != settings.LIB_PAY_API_KEY:
            return Response({"success": False, "error": "Invalid or missing API key."}, status=status.HTTP_403_FORBIDDEN)
        
        # Get terminal serial from request data
        terminal_serial = request.data.get('terminal_serial')
        user_type = request.data.get('user_type')  # This can be "Merchant" or "Lotto agent"
        
        if not terminal_serial:
            return Response({
                "success": False,
                "error": "Terminal serial is required."
            }, status=status.HTTP_400_BAD_REQUEST)
            
        if not user_type or user_type not in ["MERCHANT", "LOTTO_AGENT"]:
            return Response({
                "success": False,
                "error": "Valid user type is required (MERCHANT or LOTTO_AGENT)."
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            terminal = TerminalSerialTable.objects.get(terminal_serial=terminal_serial)
            
            # Update the user type
            terminal.type_of_user = user_type
            terminal.save()
            
            return Response({
                "success": True,
                "message": f"Terminal user type updated to {user_type} successfully.",
                "terminal_serial": terminal_serial,
                "type_of_user": user_type
            })
            
        except TerminalSerialTable.DoesNotExist:
            return Response({
                "success": False,
                "error": "Terminal serial not found."
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                "success": False,
                "error": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CheckPayboxMerchantAPIView(APIView):
    """
    Endpoint to check if a user is a paybox merchant.
    If already checked, returns a message. If not, marks as checked and returns success.
    """
    def get(self, request):
        # Extract API Key from headers
        api_key = request.headers.get("X-API-KEY")

        # Validate API Key
        if not api_key or api_key != settings.LIB_PAY_API_KEY:
            return Response({"success": False, "error": "Invalid or missing API key."}, status=status.HTTP_403_FORBIDDEN)
        
        # Get the user email from request parameters
        user_email = request.query_params.get('email')
        if not user_email:
            return Response({"success": False, "error": "User email is required."}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.get(email=user_email)
            
            # Check if user is already marked as a paybox merchant
            if user.is_paybox_merchant:
                return Response({
                    "success": True, 
                    "message": "That it has already been checked",
                    "is_paybox_merchant": True
                })
            
            # If not checked, mark as checked
            user.is_paybox_merchant = True
            user.save()
            
            return Response({
                "success": True,
                "message": "success",
                "is_paybox_merchant": True
            })
            
        except User.DoesNotExist:
            return Response({
                "success": False,
                "error": "User not found."
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                "success": False,
                "error": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ReversalCallbackAPIView(APIView):
    permission_classes = []

    def post(self, request):
        """
        Callback to receive reversed/failed card transaction receipt for charge backs
        """
        serializer = ReversalCallbackSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class CheckTerminalPurchaseAPIView(APIView):
    """
    Endpoint to check if a user has purchased terminal.
    """

    def get(self, request):
        api_key = request.headers.get("X-API-KEY")

        # Validate API Key
        if not api_key or api_key != settings.LIB_PAY_API_KEY:
            return Response({"success": False, "error": "Invalid or missing API key."}, status=status.HTTP_403_FORBIDDEN)

        user_email = request.query_params.get('email')
        if not user_email:
            return Response({"success": False, "error": "User email is required."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(email=user_email)

            if Transaction.objects.filter(user=user, transaction_type='TERMINAL_PURCHASE', is_reversed=False, status="SUCCESSFUL").exists():
                return Response({
                    "success": True,
                    "message": "Payment for terminal was successful",
                })
            else:
                return Response({
                    "success": False,
                    "message": "Payment for terminal not found",
                }, status=status.HTTP_400_BAD_REQUEST)

        except User.DoesNotExist:
            return Response({
                "success": False,
                "message": "User not found."
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                "success": False,
                "message": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ValidateCardTransaction(APIView):
    permission_classes = []

    def post(self, request):
        api_key = request.headers.get("X-API-KEY")
        if not api_key or api_key != settings.LIB_PAY_API_KEY:
            return Response({"success": False, "error": "Invalid or missing API key."}, status=status.HTTP_403_FORBIDDEN)
        serializer = ValidateCardTransactionSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class TerminalReportAPIView(APIView):
    permission_classes = []

    def get(self, request):
        api_key = request.headers.get("X-API-KEY")
        if not api_key or api_key != settings.LIB_PAY_API_KEY:
            return Response({"success": False, "error": "Invalid or missing API key."}, status=status.HTTP_403_FORBIDDEN)
        lotto_terminals = TerminalSerialTable.objects.filter(type_of_user="LOTTO_AGENT")
        result = list()
        for data in lotto_terminals:
            assigned = True if data.user else False
            creation_date = data.date_created
            result_data = {
                    "serial_no": data.terminal_serial,
                    "terminal_id": data.terminal_id,
                    "date_created": creation_date,
                    "assigned": assigned,
                    "location": data.user.get_full_address if data.user else "",
                    "agent_name": data.user.full_name if data.user else "",
                    "agent_email": data.user.email if data.user else "",
                    "agent_phone_number": data.user.phone_number if data.user else "",
                    "agent_address": data.user.get_full_address if data.user else "",
                }

            agent_data = None
            agent_profile = SuperAgentProfile.objects.filter(agent=data.user)
            if agent_profile.exists():
                agent_data = agent_profile.last()
            if agent_data:
                agent_dict = {
                    "team_lead_name": agent_data.team_lead.full_name if agent_data.team_lead else "",
                    "team_lead_email": agent_data.team_lead.email if agent_data.team_lead else "",
                    "team_lead_phone_number": agent_data.team_lead.phone_number if agent_data.team_lead else "",
                    "team_lead_address": agent_data.team_lead.get_full_address if agent_data.team_lead else "",
                    "supervisor_name": agent_data.supervisor.full_name if agent_data.supervisor else "",
                    "supervisor_email": agent_data.supervisor.email if agent_data.supervisor else "",
                    "supervisor_phone_number": agent_data.supervisor.phone_number if agent_data.supervisor else "",
                    "supervisor_address": agent_data.supervisor.get_full_address if agent_data.supervisor else "",
                }
                result_data.update(agent_dict)

            result.append(result_data)
        return Response(result)


