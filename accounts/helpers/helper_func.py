import calendar
import datetime

from django.conf import settings
from django.core.cache import cache
from django.db import transaction
from django.db.models import Count

from main.models import UserFlag, User, ConstantTable
import json
import requests
import uuid

import binascii
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
from io import BytesIO
import random


def notify_admin_whatsapp_on_pending_trans(phone_number, pending_transactions, trans_count):
    whatsapp_url = "https://pickyassist.com/app/api/v2/push"

    whatsapp_payload = json.dumps(
        {
            "token": f"{settings.PICKY_ASSIST_TOKEN}",
            "priority ": "0",
            "application": "10",
            "data": [
                {
                    "number": f"{phone_number}",
                    "message": f"Hello, \nHere are Pending Transactions that has lasted over 10 minutes. \n\n{trans_count}\n\n\n{pending_transactions}"
                }
            ]
        }
    )
    whatsapp_Headers = {'Content-type': 'application/json'}

    whatsapp_response = requests.request("POST", whatsapp_url, headers=whatsapp_Headers, data=whatsapp_payload)
    whatsapp_res = json.loads(whatsapp_response.text)

    return "Pending Transactions Sent Out"


def notify_admin_whatsapp_on_blocked_funders(phone_number, email, amount_funded, previous_bal, current_bal, reached):
    whatsapp_url = "https://pickyassist.com/app/api/v2/push"

    if reached == True:
        message = f"ALERT!!, {email} who is a blocked-on-funding user has funded his account with {amount_funded} greater than his reciveing limit. \nTheir send money status has been deactivated. \n\nPrevious Balance - {previous_bal}\nCurrent Balance - {current_bal}"
    else:
        message = f"WARNING!!, {email} who is a blocked-on-funding user has funded his account with {amount_funded} which is not yet greater than his reciveing limit. \nTheir send money status is still True. \n\nPrevious Balance - {previous_bal}\nCurrent Balance - {current_bal}"

    whatsapp_payload = json.dumps(
        {
            "token": f"{settings.PICKY_ASSIST_TOKEN}",
            "priority ": "0",
            "application": "10",
            "data": [
                {
                    "number": f"{phone_number}",
                    "message": message
                }
            ]
        }
    )
    whatsapp_Headers = {'Content-type': 'application/json'}

    whatsapp_response = requests.request("POST", whatsapp_url, headers=whatsapp_Headers, data=whatsapp_payload)
    whatsapp_res = json.loads(whatsapp_response.text)

    return "Processing Pending Trans"


def notify_admin_on_bills_airtime_low_balance(phone_number, service_name, message=None):
    whatsapp_url = "https://pickyassist.com/app/api/v2/push"

    message = f"WARNING!!,\n\n{service_name} Balance is low. Please fund as soon as possible \n\n{message}"

    whatsapp_payload = json.dumps(
        {
            "token": f"{settings.PICKY_ASSIST_TOKEN}",
            "priority ": "0",
            "application": "10",
            "data": [
                {
                    "number": f"{phone_number}",
                    "message": message
                }
            ]
        }
    )
    whatsapp_Headers = {'Content-type': 'application/json'}

    try:

        whatsapp_response = requests.request("POST", whatsapp_url, headers=whatsapp_Headers, data=whatsapp_payload)
        whatsapp_res = json.loads(whatsapp_response.text)

    except:
        pass

    return "Bills Airtime Low Balance"


def notify_admin_on_false_credit(
        user_email, false_amount,
        user_balance_before, user_balance_after
):
    if settings.ENVIRONMENT == "development":
        pass
    else:
        whatsapp_url = "https://pickyassist.com/app/api/v2/push"

        for phone_number in ["*************", "*************"]:
            whatsapp_payload = json.dumps(
                {
                    "token": f"{settings.PICKY_ASSIST_TOKEN}",
                    "priority ": "0",
                    "application": "10",
                    "data": [
                        {
                            "number": f"{phone_number}",
                            "message": f"ALERT!!! \nThere's been a false CREDIT on {user_email}'s account. \nTheir credit of {false_amount} does not match any credit record or transaction of same amount. \nTheir previous balance was {user_balance_before} and their current balance is {user_balance_after}. Please check as soon as possible."
                        }
                    ]
                }
            )
            whatsapp_Headers = {'Content-type': 'application/json'}

            whatsapp_response = requests.request("POST", whatsapp_url, headers=whatsapp_Headers, data=whatsapp_payload)
            whatsapp_res = json.loads(whatsapp_response.text)

    return "Admins successfully notified on false credit"


def notify_agent_activity_report_ran(phone_number):
    whatsapp_url = "https://pickyassist.com/app/api/v2/push"

    message = f"NOTIFICATION!!\n\nThe Agent Activity Process Just Finished. Please look out for the email. \nIf you cannot find it, please run *python manage.py get_trans_report* \n\nThanks"

    whatsapp_payload = json.dumps(
        {
            "token": f"{settings.PICKY_ASSIST_TOKEN}",
            "priority ": "0",
            "application": "10",
            "data": [
                {
                    "number": f"{phone_number}",
                    "message": message
                }
            ]
        }
    )
    whatsapp_Headers = {'Content-type': 'application/json'}

    whatsapp_response = requests.request("POST", whatsapp_url, headers=whatsapp_Headers, data=whatsapp_payload)
    whatsapp_res = json.loads(whatsapp_response.text)

    return "Activity report notification"


def notify_admin_on_false_float_low_balance(phone_number, details):
    whatsapp_url = "https://pickyassist.com/app/api/v2/push"

    message = f"ALERTTTTTTTTTTTTTTT!!\n\nALERTTTTTTTTTTTTTTT!!\n\nFalse Float Low Balance is Triggered. Transfers will start failing. Notify Emeka. Thanks\n\nDetails:\n{details}"

    whatsapp_payload = json.dumps(
        {
            "token": f"{settings.PICKY_ASSIST_TOKEN}",
            "priority ": "0",
            "application": "10",
            "data": [
                {
                    "number": f"{phone_number}",
                    "message": message
                }
            ]
        }
    )
    whatsapp_Headers = {'Content-type': 'application/json'}

    try:
        whatsapp_response = requests.request("POST", whatsapp_url, headers=whatsapp_Headers, data=whatsapp_payload)
        whatsapp_res = json.loads(whatsapp_response.text)

    except requests.exceptions.RequestException:
        pass

    return "Admin Notified"


def notify_admin_on_customer_trans_count_limit(user, phone_number):
    whatsapp_url = "https://pickyassist.com/app/api/v2/push"

    message = f"ALERTTTTTTTTTTTTTTT!!\n\nALERTTTTTTTTTTTTTTT!!\nUser with email: {user.email} has reached their hourly transfer count. Please reach out to them."

    whatsapp_payload = json.dumps(
        {
            "token": f"{settings.PICKY_ASSIST_TOKEN}",
            "priority ": "0",
            "application": "10",
            "data": [
                {
                    "number": f"{phone_number}",
                    "message": message
                }
            ]
        }
    )
    whatsapp_Headers = {'Content-type': 'application/json'}

    try:
        whatsapp_response = requests.request("POST", whatsapp_url, headers=whatsapp_Headers, data=whatsapp_payload)
        whatsapp_res = json.loads(whatsapp_response.text)

    except requests.exceptions.RequestException:
        pass

    return "Admin Notified"


def notify_admin_group(user, details, user_id=None, suspend=False):
    alert_message = details.replace("\n", " ")

    # if user is None and user_id is not None:
    #     user = User.objects.filter(id=user_id).last()
    # else:
    #     user = None

    whatsapp_url = "https://pickyassist.com/app/api/v2/push"
    whatsapp_payload = json.dumps(
        {
            "token": f"{settings.PICKY_ASSIST_TOKEN}",
            "group_id": "120363039562308072",
            "application": "10",
            "mention_numbers": [
                "*************",
                "*************",
                "2348031346306",
                "2348090643057",
                "2348035693405"
            ],
            "globalmessage": f"@*************, @2348031346306, @*************, @2348090643057, @2348035693405, Hi Admins,\n\n{details}"
        }
    )

    whatsapp_Headers = {'Content-type': 'application/json'}

    try:
        whatsapp_response = requests.post(whatsapp_url, data=whatsapp_payload, headers=whatsapp_Headers)
        whatsapp_res = json.loads(whatsapp_response.text)

    except requests.exceptions.RequestException:
        pass

    return f"Whatsapp Group Notification Sent"


# def notify_admin_whatsapp_on_pending_trans(first_name, order_id, phone_number, amount_paid):

#     whatsapp_url = "https://pickyassist.com/app/api/v2/push"
#     whatsapp_payload = json.dumps(
#         {
#             "token": f"{settings.PICKY_ASSIST_TOKEN}",
#             "group_id": "120363043530944886",
#             "application":"10",
#             "mention_numbers":[
#                 "*************",
#             ],
#             # +234 ************
#             "globalmessage" :f"A CUSTOMER HAS MADE PAYMENT! \n@2347060900294, @2348060164419, @2348129259458, @2348117849057, @2348186311099, @2348035693405 Hi Admins, A customer with name - {first_name}, order id - {order_id} and phone number - {phone_number} has just made a payment of ₦{amount_paid}. Please confirm their order soon. Thank You!"
#         }
#     )

#     whatsapp_Headers = {'Content-type': 'application/json'}

#     whatsapp_response = requests.post(whatsapp_url, data=whatsapp_payload, headers=whatsapp_Headers)

#     return f"Whatsapp Message sent to admin on customer payment up of user: ({first_name} - {phone_number}) with order id: {order_id} for ₦{amount_paid}"


def is_valid_uuid(string):
    try:
        uuid.UUID(string)
        return True
    except ValueError:
        return False


class CyberPayClass():
    base_url = settings.CYBERPAY_BASE_URL
    integration_key = settings.CYBER_PAY_INTEGRATION_KEY

    @classmethod
    def send_out_qr_request(cls, payload):

        url = f"{cls.base_url}/payments"
        payload["IntegrationKey"] = cls.integration_key

        print(payload["IntegrationKey"])

        try:
            response = requests.request("POST", url, json=payload)
            resp = {
                "status": True,
                "data": response.json()
            }

        except requests.exceptions.RequestException as err:
            resp = {
                "status": False,
                "data": f"{err}"
            }

        return resp

    @classmethod
    def query_qr_ref(cls, reference):

        url = f"{cls.base_url}/payments/generateQRcode/{reference}"
        try:
            response = requests.request("GET", url)
            resp = {
                "status": True,
                "data": response.json()
            }

        except requests.exceptions.RequestException as err:
            resp = {
                "status": False,
                "data": f"{err}"
            }

        return resp

    @classmethod
    def tsq_on_transaction(cls, reference):

        url = f"{cls.base_url}/payments/{reference}"

        try:
            response = requests.request("GET", url)
            resp = {
                "status": True,
                "data": response.json()
            }

        except requests.exceptions.RequestException as err:
            resp = {
                "status": False,
                "data": f"{err}"
            }

        return resp


def normalize_name(name):
    """Normalize the order of words in a name."""
    return ' '.join(sorted(name.split()))


def get_names_match(user_full_name, beneficiary_full_name):
    names1 = user_full_name.split()
    names2 = beneficiary_full_name.split()

    # Count the occurrences of each name in the two lists
    count_dict = {}
    all_names = names1 + names2

    for name in all_names:
        if name in count_dict:
            count_dict[name] += 1
        else:
            count_dict[name] = 1

    # Check if any name occurs at least twice
    match_count = 0
    for count in count_dict.values():
        if count >= 2:
            match_count += 1

    # Print the result
    if match_count >= 2:
        return True
    else:
        return False


def notify_admin_whatsapp_on_new_cards_requests(phone_number, message):
    whatsapp_url = "https://pickyassist.com/app/api/v2/push"

    whatsapp_payload = json.dumps(
        {
            "token": f"{settings.PICKY_ASSIST_TOKEN}",
            "priority ": "0",
            "application": "10",
            "data": [
                {
                    "number": f"{phone_number}",
                    "message": f"{message}"
                }
            ]
        }
    )
    whatsapp_Headers = {'Content-type': 'application/json'}

    whatsapp_response = requests.request("POST", whatsapp_url, headers=whatsapp_Headers, data=whatsapp_payload)
    whatsapp_res = json.loads(whatsapp_response.text)

    return "Card Request Message Sent Out"


def get_previous_account_num_func(user, account_type, overide=False, index=1):
    if user.vfd_bvn_acct_num_count > 0 or overide == False:
        if index <= 10:

            get_users_with_bvn = User.objects.exclude(id=user.id).filter(bvn_number=user.bvn_number, vfd_bvn_acct_num_count__gt=0)
            if get_users_with_bvn:
                for user_data in get_users_with_bvn:
                    if account_type == "PERSONAL":
                        get_account_number = user_data.accounts.filter(true_account_type="PERSONAL",
                                                                       account_type__in=["COLLECTION", "OTHERS"]).first()
                        if get_account_number:
                            return get_account_number.account_number

                    elif account_type == "CORPORATE":
                        get_account_number_other = user_data.other_service_accounts.filter(vfd_account_type__in=["CORPORATE"]).first()
                        get_account_number = user_data.accounts.filter(true_account_type="CORPORATE",
                                                                       account_type__in=["COLLECTION", "OTHERS"]).first()

                        if get_account_number_other:
                            return get_account_number_other.account_number
                        elif get_account_number:
                            return get_account_number.account_number

            # return None
            # return get_previous_account_num_func(user=user, account_type=account_type, overide=overide, index=index+1)

    return None


def get_most_recent_account(user):
    from accounts.models import AccountSystem
    """
    Fetch the most recent account for a given user and account type.

    Args:
        user (User): The user for whom the account is being retrieved.
        account_type (str): The type of account to filter by. Default is 'COLLECTION'.

    Returns:
        AccountSystem: The most recent account for the user and account type, or None if no account exists.
    """
    acct_no = None
    acct = AccountSystem.objects.filter(user=user, account_provider='VFD').order_by('-date_created')
    if acct.exists():
        acct_no = acct.first().account_number
    return acct_no


def one_way_decrypt_trans_notify(reference):
    PARALLEX_NOTIFY_KEY = settings.PARALLEX_NOTIFY_KEY
    PARALLEX_NOTIFY_IV = settings.PARALLEX_NOTIFY_IV

    key = base64.b64decode(PARALLEX_NOTIFY_KEY)
    iv = base64.b64decode(PARALLEX_NOTIFY_IV)

    reference = base64.b64decode(reference)

    try:
        cipher = AES.new(key, AES.MODE_CBC, iv)
        decrypted_bytes = cipher.decrypt(reference)
        plaintext = unpad(decrypted_bytes, AES.block_size).decode('utf-8')
    except Exception as ex:
        plaintext = ''

    return plaintext


def highlight_pd_df_cells(val):
    color = 'yellow' if val == -825 else ''
    return f'background-color: {color}'


def generate_unique_ids():
    """
    Create Unique ID
    """
    gen_uuid = str(uuid.uuid4()).replace('-', '').upper()

    generated_id = ''.join(random.choice(gen_uuid) for i in range(7))
    return generated_id


class AjoClass():
    base_url = settings.AJO_BASE_URL
    header = {"Authorization": f"Hook {settings.AJO_MULTI_AUTH}"}

    @classmethod
    def get_agent_num_of_users(cls, user_id):
        url = f"{cls.base_url}/ajo/agent_info/"

        payload = {
            "user_id": user_id,
        }

        try:
            response = requests.request("POST", url=url, json=payload, headers=cls.header)
            new_resp = response.json()

            print(new_resp)

            if new_resp.get("status") == True:
                user_no = new_resp.get('data', {}).get("total_ajo_users")
            else:
                user_no = None


        except requests.exceptions.RequestException as err:
            user_no = None

        return user_no

    @classmethod
    def get_ajo_loan_tiers(cls):
        cache_key = "ajo_loan_tiers"
        cached_data = cache.get(cache_key)
        if cached_data is not None:
            tiers = cached_data
        else:
            url = f"{cls.base_url}/loans/get_loan_tiers/"
            try:
                response = requests.request("GET", url=url, headers=cls.header)
                new_resp = response.json()

                print(new_resp)

                if new_resp.get("status") == "success":
                    # tiers = {key: value["deposit"] for key, value in new_resp.get('data', {}).items()}
                    tiers = new_resp.get('data', {})
                else:
                    return {}

                cache.set(cache_key, tiers, timeout=10800)  # 3 hours

            except requests.exceptions.RequestException as err:
                if settings.ENVIRONMENT == "development":
                    tiers = {
                        '1': {'limit': 4000000, 'salary': 40000, 'deposit': 60000},
                        '2': {'limit': 800000, 'salary': 72000, 'deposit': 120000},
                        '3': {'limit': 1000000, 'salary': 100000, 'deposit': 150000}
                    }
                else:
                    return {}

        return tiers


def convert_any_date_format(input_date, output_format):
    from datetime import datetime
    common_formats = [
        "%Y-%m-%d",
        "%d/%m/%Y",
        "%m/%d/%Y",
        "%Y%m%d",
    ]

    for format in common_formats:
        try:
            parsed_date = datetime.strptime(input_date, format)
            formatted_date = parsed_date.strftime(output_format)
            return formatted_date
        except ValueError:
            continue

    return None


def deduct_commission_from_paybox_merchant(amount):
    constant_table = ConstantTable.get_constant_table_instance()
    inflow_percentage_charge = constant_table.merchant_inflow_percent
    capped_value = constant_table.merchant_inflow_capped_at

    amount_to_charge = float(amount / 100) * inflow_percentage_charge
    if amount_to_charge > capped_value:
        amount_to_charge = capped_value

    return amount_to_charge


def get_week_start_and_end_datetime(date_time):
    week_start = date_time - datetime.timedelta(days=date_time.weekday())
    week_end = week_start + datetime.timedelta(days=6)
    week_start = datetime.datetime.combine(week_start.date(), datetime.time.min)
    week_end = datetime.datetime.combine(week_end.date(), datetime.time.max)
    return week_start, week_end


def get_month_start_and_end_datetime(date_time):
    month_start = date_time.replace(day=1)
    month_end = month_start.replace(day=calendar.monthrange(month_start.year, month_start.month)[1])
    month_start = datetime.datetime.combine(month_start.date(), datetime.time.min)
    month_end = datetime.datetime.combine(month_end.date(), datetime.time.max)
    return month_start, month_end


def get_year_start_and_end_datetime(date_time):
    year_start = date_time.replace(day=1, month=1, year=date_time.year)
    year_end = date_time.replace(day=31, month=12, year=date_time.year)
    year_start = datetime.datetime.combine(year_start.date(), datetime.time.min)
    year_end = datetime.datetime.combine(year_end.date(), datetime.time.max)
    return year_start, year_end


def create_electronic_levy_transaction(transaction_instance):
    if float(transaction_instance.amount) >= 10000 and transaction_instance.status == "SUCCESSFUL" and \
            transaction_instance.user.type_of_user not in ["STAFF_AGENT", "DMO_AGENT"]:
        from accounts.models import Transaction, AccountSystem, WalletSystem

        # Create Transaction for the 50 charge, for users to see as separate transaction
        levy_amount = 50
        ref_number = Transaction.create_liberty_reference("LGLP-ELECT-LEVY")
        levy_narration = "Stamp Duty on Electronic Fund Transfer"
        balance = transaction_instance.balance_after
        new_balance = float(balance) - 50
        wallet_id = transaction_instance.wallet_id
        wallet_type = transaction_instance.wallet_type
        provider = transaction_instance.account_provider
        user = transaction_instance.user
        levy_transaction = Transaction.objects.create(
            transaction_type="ELECTRONIC_TRANSFER_LEVY", amount=levy_amount, status="SUCCESSFUL", user=user, liberty_reference=ref_number,
            account_provider=provider, narration=levy_narration, balance_before=balance, balance_after=new_balance
        )
        # Removed levy amount from liberty commission, so that it will not reflect as charges on receipt
        transaction_instance.liberty_commission -= levy_amount
        transaction_instance.save()
        if transaction_instance.account_provider in ["WEMA", "FIDELITY"]:
            WalletSystem.pay_electronic_levy_commission_to_liberty(
                user.id, wallet_id=wallet_id, wallet_type=wallet_type, amount=levy_amount, transaction_id=levy_transaction.id
            )
    return True


def pay_terminal_commission(transaction_type, amount, user, sender_wallet_instance, narration, receiver_wallet_instance, payment_receiver):
    from accounts.models import Transaction, WalletSystem
    transaction_instance = Transaction.objects.create(
        transaction_type=transaction_type, amount=amount, liberty_commission=0, user=user, user_full_name=user.full_name, user_email=user.email,
        wallet_id=sender_wallet_instance.wallet_id, wallet_type=sender_wallet_instance.wallet_type, narration=narration,
        total_amount_charged=amount, total_amount_sent_out=amount, beneficiary_account_name=payment_receiver.bvn_full_name,
        beneficiary_nuban=payment_receiver.phone_number, beneficiary_wallet_id=receiver_wallet_instance.wallet_id,
        beneficiary_wallet_type=receiver_wallet_instance.wallet_type
    )

    if transaction_type == "TERMINAL_PURCHASE":
        reference = Transaction.create_liberty_reference(suffix="LGLP_TERM_PAY")
    else:
        reference = Transaction.create_liberty_reference(suffix="LGLP_TERM_COMM")

    deduct_balance = WalletSystem.deduct_balance(
        user=user,
        wallet=sender_wallet_instance,
        amount=amount,
        trans_type=transaction_type,
        transaction_instance_id=transaction_instance.transaction_id,
    )
    user_balance_before = deduct_balance["balance_before"]
    user_balance_after = WalletSystem.get_balance_after(
        user=user,
        balance_before=user_balance_before,
        total_amount=amount,
        is_credit=False
    )
    escrow_instance = WalletSystem.move_to_escrow_send_pay_buddy(
        user=user,
        balance_before=user_balance_before,
        balance_after=user_balance_after,
        from_wallet_id=sender_wallet_instance.wallet_id,
        to_wallet_id=receiver_wallet_instance.wallet_id,
        from_wallet_type=sender_wallet_instance.wallet_type,
        to_wallet_type=receiver_wallet_instance.wallet_type,
        transfer_type=transaction_type,
        amount=amount,
        to_nuban=payment_receiver.phone_number,
        to_account_name=payment_receiver.bvn_full_name if payment_receiver.bvn_first_name else payment_receiver.full_name,
        liberty_commission=0,
        total_amount_charged=amount,
        narration=narration,
        is_beneficiary=False,
        is_recurring=False,
        debit_credit_record_id=deduct_balance.get("debit_credit_record_id"),
    )

    transaction_instance.status = 'SUCCESSFUL'
    transaction_instance.escrow_instance = escrow_instance
    transaction_instance.balance_after = user_balance_after
    transaction_instance.balance_before = user_balance_before
    transaction_instance.escrow_id = escrow_instance.escrow_id
    transaction_instance.liberty_reference = reference
    transaction_instance.save()

    # Fund receiver wallet
    WalletSystem.fund_balance(
        user=payment_receiver,
        wallet=receiver_wallet_instance,
        amount=amount,
        trans_type=transaction_type,
        transaction_instance_id=transaction_instance.transaction_id,
        unique_reference=escrow_instance.escrow_id
    )

    escrow_instance.escrow_transaction_status = transaction_instance.status
    escrow_instance.save()
    return True


def settle_terminal_commission(commission_ids: list):
    from accounts.helpers.vfdbank_manager import VFDBank
    from accounts.models import WalletSystem, Transaction, OtherCommissionsRecord, TerminalPurchaseCommission

    terminal_payment_receiver = User.objects.filter(email=settings.TERMINAL_PAYMENT_USER).last()
    liberty_wallet_instance = WalletSystem.get_wallet(user=terminal_payment_receiver, from_wallet_type="COLLECTION")
    with transaction.atomic():
        commissions = TerminalPurchaseCommission.objects.select_for_update().filter(id__in=commission_ids)
        for commission in commissions:
            user = commission.user
            liberty_reference = f"LP-TERM-PURCH-COMM-{str(uuid.uuid4())}"
            transaction_type = "TERMINAL_PURCHASE_COMMISSION"
            transaction_reason = f"Terminal purchase commission | {commission.pos_request.user.email}"
            bank_float_balance_before = VFDBank.get_vfd_float_balance()
            if commission.commission_type == "agent":
                total_amount = ConstantTable.get_constant_table_instance().agent_terminal_sales_commission
            elif commission.commission_type == "supervisor":
                total_amount = ConstantTable.get_constant_table_instance().supervisor_terminal_sales_commission
            else:
                total_amount = ConstantTable.get_constant_table_instance().manager_terminal_sales_commission

            transaction_instance = Transaction.objects.create(
                user=terminal_payment_receiver,
                amount=total_amount,
                total_amount_received=0,
                liberty_commission=0,
                bank_float_balance_before=bank_float_balance_before,
                wallet_id=liberty_wallet_instance.wallet_id,
                account_id=None,
                account_provider=None,
                wallet_type=liberty_wallet_instance.wallet_type,
                transaction_type=transaction_type,
                narration=transaction_reason,
                status="PENDING",
                balance_before=liberty_wallet_instance.available_balance,
                balance_after=0,
                beneficiary_account_name=user.bvn_full_name if user.bvn_first_name else user.full_name,
                beneficiary_nuban=user.phone_number,
                source_account_name=None,
                source_nuban=None,
                source_bank_code=None,
                escrow_id=None,
                liberty_reference=liberty_reference,
                unique_reference=uuid.uuid4(),
                provider_fee=0,
                provider_status="00",
                payload="{}",
            )
            try:
                # Debit Agent with amount due
                deduct_balance_from_user = WalletSystem.deduct_balance(
                    user=terminal_payment_receiver,
                    wallet=liberty_wallet_instance,
                    amount=total_amount,
                    trans_type="COMMISSIONS_DISBURSEMENT",
                    transaction_instance_id=transaction_instance.transaction_id,
                    unique_reference=transaction_instance.unique_reference
                )
                user_instance_balance_before = deduct_balance_from_user["balance_before"]
                user_instance_balance_after = deduct_balance_from_user["balance_after"]

                OtherCommissionsRecord.create_and_top_up_other_commissions_for_agent(
                    agent=user,
                    sales_rep=None,
                    amount=total_amount,
                    transaction_id=transaction_instance.transaction_id,
                    transaction_type=transaction_type,
                    transaction_reason=transaction_reason,
                    total_profit=0,
                    liberty_profit=0,
                    ro_cash_profit=0,
                    agent_cash_profit=total_amount
                )

                transaction_instance.balance_before = user_instance_balance_before
                transaction_instance.after = user_instance_balance_after
                transaction_instance.status = "SUCCESSFUL"
                transaction_instance.save()

                commission.settled = True
                commission.save()

            except Exception as err:
                transaction_instance.status = "FAILED"
                transaction_instance.save()
                commission.payload = str(err)
                commission.settled = False
                commission.save()
