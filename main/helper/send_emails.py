from celery import shared_task
from django.conf import settings
from main.helper.helper_function import convert_num_to_currency

from sendgrid import Send<PERSON>ridAPIClient
from string import Template
from pytz import timezone
import requests
import os
import pandas as pd


def new_send_email(email, template, subject, meta_data):
    # MailGun
    mail_gun_url = "https://api.mailgun.net/v3/libertypayng.com/messages"
    mail_gun_auth = ("api", settings.MAILGUN_API_KEY)
    mail_gun_data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": f"{meta_data} {subject}",
        "html": template
    }
    try:
        res = requests.post(mail_gun_url, auth=mail_gun_auth, data=mail_gun_data)
    except Exception as e:
        pass


def send_debit_credit_email(transaction, entry_type, date_occured):
    TEMPLATE_DIR = os.path.join("templates/credit_debit", "credit_debit.html")
    html_temp = os.path.abspath(TEMPLATE_DIR)
    with open(html_temp) as temp_file:
        template = temp_file.read()


    if entry_type == "CREDIT":
        origin_ben_name_type = "Originator Name:"
        origin_ben_name = transaction.source_account_name if transaction.source_account_name else "-"
        origin_ben_nuban_type = ""
        origin_ben_nuban = ""
    else:
        origin_ben_name_type = "Beneficiary Name:"
        origin_ben_name = transaction.beneficiary_account_name if transaction.beneficiary_account_name else "-"
        origin_ben_nuban_type = "Beneficiary Account Number:"
        origin_ben_nuban = transaction.beneficiary_nuban if transaction.beneficiary_nuban else "-"


    first_name = transaction.user.bvn_first_name if transaction.user.bvn_first_name else transaction.user.first_name

    template = Template(template).safe_substitute(
        first_name=first_name.capitalize(),
        entry_type=entry_type,
        entry_type_lower=entry_type.lower(),
        transaction_type=transaction.transaction_type,
        amount=f"₦{convert_num_to_currency(transaction.amount)}",
        service_charge=f"₦{convert_num_to_currency(transaction.liberty_commission)}" if transaction.liberty_commission else 0.00,
        sms_charge=f"₦{convert_num_to_currency(transaction.sms_charge)}" if transaction.sms_charge else 0.00,
        transaction_id=transaction.transaction_id,
        wallet_type=transaction.wallet_type,
        narration=transaction.narration,
        date=date_occured,
        balance_before=f"₦{convert_num_to_currency(transaction.balance_before)}" if transaction.balance_before else None,
        balance_after=f"₦{convert_num_to_currency(transaction.balance_after)}" if transaction.balance_after else None,
        status=transaction.status,
        origin_ben_name_type = origin_ben_name_type,
        origin_ben_name = origin_ben_name,
        origin_ben_nuban_type = origin_ben_nuban_type,
        origin_ben_nuban = origin_ben_nuban
    )
    
    subject = "Transaction Notification"

    send_out_email = new_send_email(
        email = transaction.user.email,
        template = template,
        subject = subject,
        meta_data = f"[{entry_type}]"
    )


def send_single_mailgun_email(email, template, subject):
    pass

def send_single_sendgrid_email(email, template, subject):
    """
    template: Template(html).safe_substitute()
    """
    receiver = [{'email': email}]
    message = {
        'personalizations': [{
            'to': receiver,
            'subject': subject
        }],
        'from': {
            'email': "<EMAIL>"
        },
        'content': [
            {
                'type': 'text/html',
                'value': template
            }
        ],
    }
    try:
        sg = SendGridAPIClient(settings.SENDGRID_API_KEY)
        resp = sg.send(message)
        return True
    except Exception as e:
        return False



def send_single_sendgrid_email_with_attachment(receiver, template, subject, encoded, file_path):
    """
    template: Template(html).safe_substitute()
    """
    message = {
        'personalizations': [{
            'to': receiver,
            'subject': subject
        }],
        'from': {
            'email': "<EMAIL>"
        },
        'content': [
            {
                'type': 'text/html',
                'value': template
            }
        ],
        'attachments': [{
            "filename": file_path,
            "content": encoded,
            "contentId": file_path,
            "disposition": "attachment"
        }]
    }
    try:
        sg = SendGridAPIClient(settings.SENDGRID_API_KEY)
        resp = sg.send(message)
        return True
    except Exception as e:
        return False



def send_email(email, passcode):


    TEMPLATE_DIR = os.path.join("templates", "confirm_email.html")
    html_temp = os.path.abspath(TEMPLATE_DIR)
    with open(html_temp) as temp_file:
        template = temp_file.read()

    template = Template(template).safe_substitute(email=email, passcode=passcode)
    
    # SendGrid
    # message = Mail(
    # <AUTHOR> <EMAIL>",
    #     to_emails=email,
    #     subject="Confirm Email",
    #     html_content=HtmlContent(template),
    # )


    # MailGun
    mail_gun_url = "https://api.mailgun.net/v3/libertypayng.com/messages"
    # mail_gun_url = "https://api.mailgun.net/v3/mg.whispersms.com/messages"
    mail_gun_auth = ("api", settings.MAILGUN_API_KEY)
    
    mail_gun_data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": "Confirm Email",
        "html": template
    }
    try:
        # res = SendGridAPIClient(settings.SENDGRID_API_KEY)
        # res.send(message)
        res = requests.post(mail_gun_url, auth=mail_gun_auth, data=mail_gun_data)
    except Exception as e:
        pass


def send_dynamic_email(email, template_dir, subject, message=None):

    TEMPLATE_DIR = os.path.join("templates", template_dir)
    html_temp = os.path.abspath(TEMPLATE_DIR)
    with open(html_temp) as temp_file:
        template = temp_file.read()

    template = Template(template).safe_substitute(email=email)
    if message:
        template = Template(template).safe_substitute(email=email, message=message)

    # SendGrid
    # message = Mail(
    # <AUTHOR> <EMAIL>",
    #     to_emails=email,
    #     subject="Confirm Email",
    #     html_content=HtmlContent(template),
    # )


    # MailGun
    mail_gun_url = "https://api.mailgun.net/v3/libertypayng.com/messages"
    # mail_gun_url = "https://api.mailgun.net/v3/mg.whispersms.com/messages"
    mail_gun_auth = ("api", settings.MAILGUN_API_KEY)
    mail_gun_data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": subject,
        "html": template
    }
    try:
        # res = SendGridAPIClient(settings.SENDGRID_API_KEY)
        # res.send(message)
        res = requests.post(mail_gun_url, auth=mail_gun_auth, data=mail_gun_data)
    except Exception as e:
        pass


def send_login_notify_email(user_email, user_full_name, login_time):
    template_dir = os.path.join(settings.BASE_DIR, "templates/notifications/login_notify.html")

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()
    
    local_time_logged = login_time.astimezone(timezone('Africa/Lagos'))
    time_logged_in = local_time_logged.strftime("%I:%M:%S %p")
    date_logged_in = local_time_logged.strftime("%B %d, %Y")
    email_subject="LIBERTY PAY LOG IN CONFIRMATION"

    template = Template(template).safe_substitute(full_name=user_full_name, time_logged_in=time_logged_in, date_logged_in=date_logged_in, email_subject=email_subject)     

    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": user_email,
        "subject": email_subject,
        "html": template,
    }
    
    try:
        response = requests.post(
            f"https://api.mailgun.net/v3/libertypayng.com/messages",
            auth=("api", f"{settings.MAILGUN_API_KEY}"),
            data = data,
            timeout=5
        )

    except requests.exceptions.RequestException as e:
        pass

    return True


def send_email_with_parameters(email, subject, message_body, from_email, attachment=None, attachment_name=None, file_name="wallet_recon.xlsx"):

    EMAIL_TEMPLATE_DIR = os.path.join(settings.BASE_DIR, "templates/dynamic_email_template.html")
    html_temp = os.path.abspath(EMAIL_TEMPLATE_DIR)
    with open(html_temp) as temp_file:
        template = temp_file.read()

    template = Template(template).safe_substitute(email=email, message_body=message_body)
    

    # MailGun
    mail_gun_url = "https://api.mailgun.net/v3/libertypayng.com/messages"
    mail_gun_auth = ("api", settings.MAILGUN_API_KEY)
    mail_gun_data = {
        "from": f"Liberty Pay <{from_email}>",
        "to": email,
        "subject": subject,
        "html": template
    }

    if attachment is not None:
        if isinstance(attachment, pd.DataFrame):
            
            first_file_name = file_name
            attachment_filename = f"{attachment_name}.xlsx" if attachment_name else "attachment.xlsx"
            
            attachment.to_excel(first_file_name, index=False)

            mail_gun_data["attachment"] = (attachment_filename, open(first_file_name, 'rb'), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

        elif os.path.exists(attachment):
            attachment_filename = os.path.basename(attachment)
            with open(attachment, 'rb') as attachment_file:
                mail_gun_data["attachment"] = (attachment_filename, attachment_file.read(), 'application/octet-stream')
        else:
            raise ValueError("Invalid attachment parameter. Please provide a DataFrame or a valid file path.")

    try:
        res = requests.post(mail_gun_url, auth=mail_gun_auth, files=mail_gun_data)
    except Exception as e:
        pass

    # Delete the temporary attachment file (if applicable)
    if attachment_filename and isinstance(attachment, pd.DataFrame):
        try:
            os.remove(f"{first_file_name}")
        except PermissionError:
            pass


    return True



# def send_email_sendgrid(email, passcode):


#     TEMPLATE_DIR = os.path.join("templates", "confirm_email.html")
#     html_temp = os.path.abspath(TEMPLATE_DIR)
#     with open(html_temp) as temp_file:
#         template = temp_file.read()

#     template = Template(template).safe_substitute(email=email, passcode=passcode)
    
#     # SendGrid
#     message = Mail(
# <AUTHOR> <EMAIL>",
#         to_emails=email,
#         subject="Confirm Email",
#         html_content=HtmlContent(template),
#     )

#     # MailGun
#     # mail_gun_url = "https://api.mailgun.net/v3/libertypayng.com/messages"
#     # # mail_gun_url = "https://api.mailgun.net/v3/mg.whispersms.com/messages"
#     # mail_gun_auth = ("api", settings.MAILGUN_API_KEY)
#     # mail_gun_data = {
# <AUTHOR> <EMAIL>",
#     #     "to": email,
#     #     "subject": "Confirm Email",
#     #     "html": template
#     # }

#     try:
#         res = SendGridAPIClient(settings.SENDGRID_API_KEY)
#         res.send(message)
#         # res = requests.post(mail_gun_url, auth=mail_gun_auth, data=mail_gun_data)
#         print(res)
#     except Exception as e:
#         print(str(e))  

@shared_task
def send_bulk_uptime_and_downtime_email(email, send_type="downtime"):

    if send_type == 'uptime':
        send_dynamic_email(email, 'service_uptime.html', 'Service Restoration')
    elif send_type == "maintenance":
        send_dynamic_email(email, 'system_maintenance.html', 'System Maintenance Notification')
    elif send_type == "cashout_failure":
        send_dynamic_email(email, 'cashout_failure_notification.html', 'Temporary Downtime Notification – Cashout Service')
    else:
        send_dynamic_email(email, 'service_downtime.html', 'Downtime notification on transfers and collections')
    return True






