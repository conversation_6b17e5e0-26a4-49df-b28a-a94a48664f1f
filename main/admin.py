import pandas as pd
from django.contrib import admin, messages
from django.contrib.admin.widgets import FilteredSelectMultiple
from django_celery_results.models import TaskResult
from django import forms

from import_export import resources
from import_export.admin import ImportExportMixin, ImportExportModelAdmin

from main.models import *
from django.contrib.admin.models import LogEntry, DELETION
from django.utils.html import escape
from django.urls import reverse
from django.utils.safestring import mark_safe
from rest_framework.authtoken.models import Token
from .models import IssueLog
from django.http import HttpResponseRedirect
from django.shortcuts import render
from django.urls import path


class TaskResultAdmin(admin.ModelAdmin):
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        search_kwargs = request.GET.get('task_kwargs', None)
        if search_kwargs:
            queryset = queryset.filter(task_kwargs__contains=search_kwargs)
        return queryset

# admin.site.register(TaskResult, TaskResultAdmin)

@admin.register(LogEntry)
class LogEntryAdmin(admin.ModelAdmin):
    date_hierarchy = 'action_time'

    list_filter = [
        # 'user',
        'content_type',
        'action_flag'
    ]

    search_fields = [
        'user__email',
        'object_repr',
        'change_message'
    ]

    list_display = [
        'action_time',
        'user',
        'change_message',
        'content_type',
        # 'object_link',
        'action_flag',
    ]


    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_view_permission(self, request, obj=None):
        return request.user.is_superuser

    # def object_link(self, obj):
    #     if obj.action_flag == DELETION:
    #         link = escape(obj.object_repr)
    #     else:
    #         # ct = obj.content_type
    #         # link = '<a href="%s">%s</a>' % (
    #         #     reverse('admin:%s_%s_change' % (ct.app_label, ct.model), args=[obj.object_id]),
    #         #     escape(obj.object_repr),
    #         # )
    #         pass
    #     return mark_safe(link)
    # object_link.admin_order_field = "object_repr"
    # object_link.short_description = "object"



###############################################################################
# RESOURCES

class RegistrationDataResource(resources.ModelResource):
    class Meta:
        model = RegistrationData

class UserResource(resources.ModelResource):
    class Meta:
        model = User

class ConstantResource(resources.ModelResource):
    class Meta:
        model = ConstantTable


class ChargeBandResource(resources.ModelResource):
    class Meta:
        model = ChargeBand


class AgentProfileResource(resources.ModelResource):
    class Meta:
        model = AgentProfile


class UnregisteredPhoneNumbersResource(resources.ModelResource):
    class Meta:
        model = UnregisteredPhoneNumber

class WhitelistResource(resources.ModelResource):
    class Meta:
        model = Whitelist

class ResetPinStorageResource(resources.ModelResource):
    class Meta:
        model = ResetPinStorage

class SMSRecordResource(resources.ModelResource):
    class Meta:
        model = SMSRecord

class UnsentSMSResource(resources.ModelResource):
    class Meta:
        model = UnsentSMS

class OtherServiceDetailResource(resources.ModelResource):
    class Meta:
        model = OtherServiceDetail

class TrackUserClickResource(resources.ModelResource):
    class Meta:
        model = TrackUserClick

class CallbackSystemResource(resources.ModelResource):
    class Meta:
        model = CallbackSystem

class SuperAgentProfileResource(resources.ModelResource):
    class Meta:
        model = SuperAgentProfile

class UserFormattedResource(resources.ModelResource):
    class Meta:
        model = UserFormatted

class AvailableBalanceResource(resources.ModelResource):
    class Meta:
        model = AvailableBalance

class DeliveryAddressDataResource(resources.ModelResource):
    class Meta:
        model = DeliveryAddressData

class UserFlagResource(resources.ModelResource):
    class Meta:
        model = UserFlag

class BlacklistResource(resources.ModelResource):
    class Meta:
        model = Blacklist

class UserWhitelistResource(resources.ModelResource):
    class Meta:
        model = UserWhitelist

class AgentSupervisorResource(resources.ModelResource):
    class Meta:
        model = AgentSupervisor

class UserOtherAccountResource(resources.ModelResource):
    class Meta:
        model = UserOtherAccount

class CorporateAccountResource(resources.ModelResource):
    class Meta:
        model = CorporateAccount

class UserTempTokenResource(resources.ModelResource):
    class Meta:
        model = UserTempToken

class DisplayBannerResource(resources.ModelResource):
    class Meta:
        model = DisplayBanner


class DataDeletionResource(resources.ModelResource):
    class Meta:
        model = UserDataDeletionRequest

class AjoAgentFormResource(resources.ModelResource):
    class Meta:
        model = AjoAgentForm

class TerminalRetrievalRequestResource(resources.ModelResource):
    class Meta:
        model = TerminalRetrievalRequest



class NewLocationListResource(resources.ModelResource):
    class Meta:
        model = NewLocationList

class BranchTeamResource(resources.ModelResource):
    class Meta:
        model = BranchTeam

class UserTeamResource(resources.ModelResource):
    class Meta:
        model = UserTeam

#######################################################################
# ADMINS



class RegistrationDataResourceAdmin(ImportExportModelAdmin):
    resource_class = RegistrationDataResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserAdmin(ImportExportModelAdmin):

    def suspend_terminals(self, request, queryset):
        for user in queryset:
            User.suspend_user(
                user=user,
                reason="Suspended By Super Admin. Do not unsuspend without instruction"
            )


        self.message_user(request, "Successfully Suspended Terminals")

    suspend_terminals.short_description = "Suspend terminals"


    def save_model(self, request, obj, form, change):
        custom_request = request
        obj.save(custom_request=custom_request)



    def get_readonly_fields(self, request, obj=None):
        if obj and obj.is_fraud: # editing an existing object
            return self.readonly_fields + ['is_fraud', 'send_money_status', 'is_suspended', 'terminal_suspended', 'mobile_suspended', 'mobile_disabled', 'terminal_disabled']
        return self.readonly_fields

    readonly_fields = [
        "terminal_provider",  "unique_id", "customer_id", "password", "lotto_win_toggle",
        "registration_email_otp", "date_of_consent", "date_assigned", "sales_rep_full_name", "sales_rep_comm_balance_daily",
        "sales_rep_comm_balance", "bills_pay_comm_balance_daily", "bills_pay_comm_balance", "other_comm_balance_daily",
        "other_comm_balance", "kyc_level", "kyc_one_progress", "kyc_two_progress", "wallet_balance"
    ]

    exclude = ('registration_email_otp', 'password')

    resource_class = UserResource
    search_fields = ['email', 'phone_number', 'terminal_id', "first_name", "last_name", "bvn_number", "sales_rep_upline_code", "username"]
    list_filter = (
        ('date_joined', "type_of_user", "kyc_two_progress", "send_money_status", "sms_subscription", "kyc_level", "has_transaction_pin", "terminal_suspended", "is_suspended", "is_fraud", "reactivation_request")
    )
    date_hierarchy = 'date_joined'
    actions = [suspend_terminals]


    def get_list_display(self, request):
        return [field.name if field.name not in ["bvn_number"] else "phone_number" for field in self.model._meta.concrete_fields]


    # def save_model(self, request, obj, form, change):
    #     if change:
    #         obj.save(update_fields=form.changed_data)
    #     else:
    #         super().save_model(request, obj, form, change)


class ConstantResourceAdmin(ImportExportModelAdmin):
    resource_class = ConstantResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ChargeBandResourceAdmin(ImportExportModelAdmin):
    resource_class = ChargeBandResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgentProfileResourceAdmin(ImportExportModelAdmin):
    search_fields = ['email', 'phone_number']
    resource_class = AgentProfileResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UnregisteredPhoneNumbersResourceAdmin(ImportExportModelAdmin):
    resource_class = UnregisteredPhoneNumbersResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class WhitelistResourceAdmin(ImportExportModelAdmin):
    resource_class = WhitelistResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class ResetPinStorageResourceAdmin(ImportExportModelAdmin):
    resource_class = ResetPinStorageResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class SMSRecordResourceAdmin(ImportExportModelAdmin):
    resource_class = SMSRecordResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UnsentSMSResourceAdmin(ImportExportModelAdmin):
    resource_class = UnsentSMSResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class OtherServiceDetailResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = OtherServiceDetailResource
    search_fields = ['user__email', 'service_name', 'wallet_id']


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class TrackUserClickResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = TrackUserClickResource
    search_fields = ['user__email', 'view_or_screen']
    list_filter = (
        ('date_added', "view_or_screen")
    )


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class CallbackSystemResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = CallbackSystemResource
    search_fields = ['user__email', 'url']
    list_filter = (
        ('date_added', "transaction_type", "other_transaction_type")
    )


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class SuperAgentProfileResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['agent', 'super_agent', 'supervisor', 'team_lead']
    resource_class = SuperAgentProfileResource
    actions = ["resave_super_agent_profile"]
    search_fields = ['team_lead__email', 'supervisor__email', 'agent__email', 'super_agent__email']
    # list_filter = (
    #     ('date_added', "transaction_type", "other_transaction_type")
    # )

    def resave_super_agent_profile(self, request, queryset):
        for agent in queryset:
            agent.save()
            self.message_user(request, f"SuperAgentProfile saved {agent.id}", level=messages.SUCCESS)

    resave_super_agent_profile.short_description = "RE-SAVE SuperAgentProfile FOR SELECTED ITEM(S)"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserFormattedResourceAdmin(ImportExportModelAdmin):
    readonly_fields = ["type_of_user"]
    resource_class = UserResource
    search_fields = ['email', 'phone_number', "first_name", "last_name"]
    list_filter = (
        ('date_joined', "type_of_user", "kyc_level")
    )
    date_hierarchy = 'date_joined'


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AvailableBalanceResourceAdmin(ImportExportModelAdmin):
    resource_class = AvailableBalanceResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class DeliveryAddressDataResourceAdmin(ImportExportModelAdmin):
    resource_class = DeliveryAddressDataResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserFlagResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ['user']
    resource_class = UserFlagResource
    search_fields = ['user__email']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


    def get_search_results(self, request, queryset, search_term):
        search_list = [term.strip() for term in search_term.split(",")]

        if len(search_list) == 1 and not search_list[0].strip():
            return queryset, True
        filtered_qs = queryset.filter(
                        user__email__in=search_list,
                        suspended=True,
                        ).distinct("user__email")
        print(len(search_list))

        return filtered_qs, True


    def mass_unsuspend_terminals(self, request, queryset):
        for query in queryset:
            UserFlag.unsuspend_user(
                user=query.user,
                reason="Unsuspended by admin",
                request=request
            )
        self.message_user(request, "Suspension successfully removed from terminals")

    mass_unsuspend_terminals.add_description = "Manually unsuspend multiple terminals"
    actions = [mass_unsuspend_terminals]


class BlacklistResourceAdmin(ImportExportModelAdmin):
    resource_class = BlacklistResource
    search_fields = ['content']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserWhitelistResourceAdmin(ImportExportModelAdmin):
    resource_class = UserWhitelistResource
    search_fields = ['content']

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class AgentSupervisorResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentSupervisorResource
    autocomplete_fields = ['supervisor', 'agents']
    search_fields = ['supervisor__email', 'agents__email']
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserOtherAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = UserOtherAccountResource
    autocomplete_fields = ['owner', 'other_account']
    search_fields = ['owner__email', 'other_account__email']
    list_filter = (
        ('date_created',)
    )
    date_hierarchy = 'date_created'


    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CorporateAccountResourceAdmin(ImportExportModelAdmin):
    resource_class = CorporateAccountResource
    autocomplete_fields = ['user', 'other_users']
    search_fields = ['user__email', 'rc_number', 'corporate_id']
    list_filter = (
        ('date_created', 'raw_incorp_date')
    )
    date_hierarchy = 'date_created'
    readonly_fields = ["corporate_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class TokenAdmin(admin.ModelAdmin):
    autocomplete_fields = ["user"]
    search_fields = ('user__email', 'key')

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserTempTokenResourceAdmin(admin.ModelAdmin):
    autocomplete_fields = ["user"]
    search_fields = ('token',)
    list_filter = (
        ('date_created', 'exp', 'pin_type')
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class DisplayBannerResourceAdmin(ImportExportModelAdmin):
    resource_class = DisplayBannerResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserDataDeletionRequestResourceAdmin(ImportExportModelAdmin):
    resource_class = DisplayBannerResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class AjoAgentFormResourceAdmin(ImportExportModelAdmin):
    search_fields = ["phone_number"]
    resource_class = AjoAgentFormResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class TerminalRetrievalRequestResourceAdmin(ImportExportModelAdmin):
    search_fields = ["phone_number", "user__email"]
    resource_class = TerminalRetrievalRequestResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NewLocationListResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["supervisor"]
    resource_class = NewLocationListResource
    search_fields = ["location", "sub_location"]

    list_filter = (
        ('date_created', 'location')
    )
    readonly_fields = ["created_by"]

    def save_model(self, request, obj, form, change):
        custom_request = request
        obj.save(custom_request=custom_request)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BranchTeamResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["supervisor", "branch"]
    search_fields = ["branch__location", "supervisor__email"]
    resource_class = BranchTeamResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class UserTeamResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["team", "users"]
    search_fields = ["team__branch__location", "users__email", "team__supervisor__email"]
    resource_class = UserTeamResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class IssueLogAdmin(admin.ModelAdmin):
    list_display = ('user', 'description', 'date_created', 'date_updated')
    list_filter = ('date_created', 'date_updated')
    search_fields = ('user__email', 'user__username', 'description')
    readonly_fields = ('date_created', 'date_updated')
    raw_id_fields = ('user',)

    fieldsets = (
        ('User Information', {
            'fields': ('user',)
        }),
        ('Issue Details', {
            'fields': ('description',)
        }),
        ('Timestamps', {
            'fields': ('date_created', 'date_updated'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


class MerchantAcquisitionOfficerForm(forms.ModelForm):
    class Meta:
        model = MerchantAcquisitionOfficer
        fields = '__all__'
        widgets = {
            'merchants': FilteredSelectMultiple("Merchants", is_stacked=False),
        }


class MerchantAcquisitionOfficerAdmin(admin.ModelAdmin):
    form = MerchantAcquisitionOfficerForm
    list_display = ['user_email', 'get_merchant_emails', 'date_created', 'date_updated']
    search_fields = ['user__email', 'merchants__email']
    filter_horizontal = ['merchants']
    autocomplete_fields = ['user']

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = 'Acquisition Officer'

    def get_merchant_emails(self, obj):
        return ", ".join([m.email for m in obj.merchants.all()])
    get_merchant_emails.short_description = 'Mapped Merchants'


class BVNWatchlistUploadForm(forms.Form):
    # Form for uploading BVN watchlist CSV/XLSX files

    file = forms.FileField(
        help_text="Upload CSV or XLSX file with BVN watchlist data. Required columns: BVN, REQUESTING BANK, FIRST NAME, MIDDLE NAME, SURNAME, CATEGORY, WATCHLIST DATE"
    )

    def clean_file(self):
        file = self.cleaned_data['file']

        if not file.name.lower().endswith(('.csv', '.xlsx', '.xls')):
            raise forms.ValidationError("Please upload a CSV or XLSX file.")

        if file.size > 10 * 1024 * 1024:
            raise forms.ValidationError("File size must be less than 10MB.")

        return file


class BVNWatchlistAdmin(admin.ModelAdmin):
    list_display = ('bvn', 'full_name_display', 'category', 'requesting_bank', 'date_watchlisted', 'date_created')
    list_filter = ('category', 'date_watchlisted', 'date_created', 'requesting_bank')
    search_fields = ('bvn', 'first_name', 'surname', 'other_name', 'requesting_bank')
    readonly_fields = ('date_created', 'date_updated')
    date_hierarchy = 'date_watchlisted'

    fieldsets = (
        ('BVN Information', {
            'fields': ('bvn', 'first_name', 'other_name', 'surname')
        }),
        ('Watchlist Details', {
            'fields': ('category', 'date_watchlisted', 'requesting_bank')
        }),
        ('Timestamps', {
            'fields': ('date_created', 'date_updated'),
            'classes': ('collapse',)
        })
    )

    actions = ['upload_bvn_watchlist']

    def full_name_display(self, obj):
        return obj.full_name
    full_name_display.short_description = 'Full Name'
    full_name_display.admin_order_field = 'first_name'

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('upload-bvn-watchlist/', self.admin_site.admin_view(self.upload_bvn_watchlist_view), name='upload_bvn_watchlist'),
        ]
        return custom_urls + urls

    def upload_bvn_watchlist(self, request, queryset):
        return HttpResponseRedirect(reverse('admin:upload_bvn_watchlist'))
    upload_bvn_watchlist.short_description = "Upload BVN Watchlist from CSV/XLSX"

    def upload_bvn_watchlist_view(self, request):
        if request.method == 'POST':
            form = BVNWatchlistUploadForm(request.POST, request.FILES)
            if form.is_valid():
                try:
                    file = form.cleaned_data['file']
                    result = self.process_uploaded_file(file)

                    messages.success(
                        request,
                        f"Successfully processed {result['total']} records. "
                        f"Created: {result['created']}, Updated: {result['updated']}, "
                        f"Errors: {result['errors']}"
                    )

                    if result['error_details']:
                        for error in result['error_details'][:10]:  # Show first 10 errors
                            messages.warning(request, f"Row {error['row']}: {error['message']}")

                        if len(result['error_details']) > 10:
                            messages.warning(request, f"... and {len(result['error_details']) - 10} more errors")

                    return HttpResponseRedirect(reverse('admin:main_bvnwatchlist_changelist'))

                except Exception as e:
                    messages.error(request, f"Error processing file: {str(e)}")
        else:
            form = BVNWatchlistUploadForm()

        context = {
            'form': form,
            'title': 'Upload BVN Watchlist',
            'opts': self.model._meta,
            'has_view_permission': self.has_view_permission(request),
        }

        return render(request, 'admin/bvn_watchlist_upload.html', context)

    def process_uploaded_file(self, file):
        result = {
            'total': 0,
            'created': 0,
            'updated': 0,
            'errors': 0,
            'error_details': []
        }

        try:
            # Read file based on extension
            if file.name.lower().endswith('.csv'):
                df = pd.read_csv(file)
            else:
                # For Excel files, try to read from different sheets
                try:
                    # First, try to read the first sheet
                    df = pd.read_excel(file, sheet_name=0)
                except Exception:
                    # If that fails, try to find a sheet with data
                    excel_file = pd.ExcelFile(file)
                    sheet_names = excel_file.sheet_names

                    df = None
                    for sheet_name in sheet_names:
                        try:
                            temp_df = pd.read_excel(file, sheet_name=sheet_name)
                            if not temp_df.empty and len(temp_df.columns) >= 6:  # At least 6 columns expected
                                df = temp_df
                                break
                        except Exception:
                            continue

                    if df is None:
                        raise ValueError("Could not find a valid sheet with data in the Excel file")

            # Check if dataframe is empty
            if df.empty:
                raise ValueError("The uploaded file appears to be empty")

            # Normalize column names (remove spaces, convert to uppercase)
            df.columns = df.columns.str.strip().str.upper().str.replace(' ', '_')

            # Also try common variations of column names
            column_variations = {
                'BVN': ['BVN', 'BVN_NUMBER', 'BANK_VERIFICATION_NUMBER'],
                'REQUESTING_BANK': ['REQUESTING_BANK', 'BANK', 'BANK_NAME', 'REQUESTING_BANK_NAME'],
                'FIRST_NAME': ['FIRST_NAME', 'FIRSTNAME', 'FNAME'],
                'MIDDLE_NAME': ['MIDDLE_NAME', 'MIDDLENAME', 'OTHER_NAME', 'OTHERNAME', 'MNAME'],
                'SURNAME': ['SURNAME', 'LAST_NAME', 'LASTNAME', 'LNAME'],
                'CATEGORY': ['CATEGORY', 'WATCHLIST_CATEGORY', 'TYPE'],
                'WATCHLIST_DATE': ['WATCHLIST_DATE', 'DATE_WATCHLISTED', 'DATE', 'WATCHLIST_DATE']
            }

            # Map actual columns to expected columns
            column_mapping = {}
            for expected_col, variations in column_variations.items():
                found = False
                for variation in variations:
                    if variation in df.columns:
                        column_mapping[variation] = expected_col
                        found = True
                        break
                if not found:
                    column_mapping[expected_col] = expected_col  # Use original if not found

            # Rename columns to standardized names
            reverse_mapping = {v: k for k, v in column_mapping.items()}
            df = df.rename(columns=reverse_mapping)

            # Final column mapping for processing
            final_column_mapping = {
                'BVN': 'bvn',
                'REQUESTING_BANK': 'requesting_bank',
                'FIRST_NAME': 'first_name',
                'MIDDLE_NAME': 'other_name',
                'SURNAME': 'surname',
                'CATEGORY': 'category',
                'WATCHLIST_DATE': 'date_watchlisted'
            }

            # Check if all required columns exist
            missing_columns = [col for col in final_column_mapping.keys() if col not in df.columns]
            if missing_columns:
                available_columns = ', '.join(df.columns.tolist())
                raise ValueError(
                    f"Missing required columns: {', '.join(missing_columns)}. "
                    f"Available columns in file: {available_columns}. "
                    f"Please ensure your file has the correct column headers."
                )

            result['total'] = len(df)

            for index, row in df.iterrows():
                try:
                    # Extract and clean data
                    bvn = str(row['BVN']).strip()
                    if len(bvn) != 11 or not bvn.isdigit():
                        result['errors'] += 1
                        result['error_details'].append({
                            'row': index + 2,
                            'message': f"Invalid BVN format: {bvn}"
                        })
                        continue

                    # Parse date
                    date_watchlisted = pd.to_datetime(row['WATCHLIST_DATE']).date()

                    # Map category
                    category = str(row['CATEGORY'])
                    category_choices = dict(BVNWatchlist.CATEGORY_CHOICES)
                    if category not in category_choices:
                        category = '1'

                    watchlist_entry, created = BVNWatchlist.objects.get_or_create(
                        bvn=bvn,
                        defaults={
                            'first_name': str(row['FIRST_NAME']).strip(),
                            'other_name': str(row['MIDDLE_NAME']).strip() if pd.notna(row['MIDDLE_NAME']) else '',
                            'surname': str(row['SURNAME']).strip(),
                            'category': category,
                            'date_watchlisted': date_watchlisted,
                            'requesting_bank': str(row['REQUESTING_BANK']).strip() if pd.notna(row['REQUESTING_BANK']) else '',
                        }
                    )

                    if created:
                        result['created'] += 1
                    else:
                        watchlist_entry.first_name = str(row['FIRST_NAME']).strip()
                        watchlist_entry.other_name = str(row['MIDDLE_NAME']).strip() if pd.notna(row['MIDDLE_NAME']) else ''
                        watchlist_entry.surname = str(row['SURNAME']).strip()
                        watchlist_entry.category = category
                        watchlist_entry.date_watchlisted = date_watchlisted
                        watchlist_entry.requesting_bank = str(row['REQUESTING_BANK']).strip() if pd.notna(row['REQUESTING_BANK']) else ''
                        watchlist_entry.save()
                        result['updated'] += 1

                except Exception as e:
                    result['errors'] += 1
                    result['error_details'].append({
                        'row': index + 2,
                        'message': str(e)
                    })

        except Exception as e:
            raise ValueError(f"Error reading file: {str(e)}")

        return result


admin.site.register(RegistrationData, RegistrationDataResourceAdmin)
admin.site.register(User, UserAdmin)
admin.site.register(IssueLog, IssueLogAdmin)
admin.site.register(ConstantTable, ConstantResourceAdmin)
admin.site.register(ChargeBand, ChargeBandResourceAdmin)
admin.site.register(AgentProfile, AgentProfileResourceAdmin)
admin.site.register(UnregisteredPhoneNumber, UnregisteredPhoneNumbersResourceAdmin)
admin.site.register(Whitelist, WhitelistResourceAdmin)
admin.site.register(ResetPinStorage, ResetPinStorageResourceAdmin)
admin.site.register(SMSRecord, SMSRecordResourceAdmin)
admin.site.register(UnsentSMS, UnsentSMSResourceAdmin)
admin.site.register(OtherServiceDetail, OtherServiceDetailResourceAdmin)
admin.site.register(TrackUserClick, TrackUserClickResourceAdmin)
admin.site.register(CallbackSystem, CallbackSystemResourceAdmin)
admin.site.register(SuperAgentProfile, SuperAgentProfileResourceAdmin)
admin.site.register(UserFormatted, UserFormattedResourceAdmin)
admin.site.register(AvailableBalance, AvailableBalanceResourceAdmin)
admin.site.register(DeliveryAddressData, DeliveryAddressDataResourceAdmin)
admin.site.register(UserFlag, UserFlagResourceAdmin)
admin.site.register(Blacklist, BlacklistResourceAdmin)
admin.site.register(UserWhitelist, UserWhitelistResourceAdmin)
admin.site.register(AgentSupervisor, AgentSupervisorResourceAdmin)
admin.site.register(UserOtherAccount, UserOtherAccountResourceAdmin)
admin.site.register(CorporateAccount, CorporateAccountResourceAdmin)
admin.site.register(Token, TokenAdmin)
admin.site.register(UserTempToken, UserTempTokenResourceAdmin)
admin.site.register(DisplayBanner, DisplayBannerResourceAdmin)
admin.site.register(UserDataDeletionRequest, UserDataDeletionRequestResourceAdmin)
admin.site.register(AjoAgentForm, AjoAgentFormResourceAdmin)
admin.site.register(TerminalRetrievalRequest, TerminalRetrievalRequestResourceAdmin)
admin.site.register(NewLocationList, NewLocationListResourceAdmin)
admin.site.register(BranchTeam, BranchTeamResourceAdmin)
admin.site.register(UserTeam, UserTeamResourceAdmin)
admin.site.register(MerchantAcquisitionOfficer, MerchantAcquisitionOfficerAdmin)
admin.site.register(ApplicationBanner)
admin.site.register(ApplicationBannerClick)
admin.site.register(BVNWatchlist, BVNWatchlistAdmin)
